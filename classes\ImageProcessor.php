<?php
/**
 * فئة معالجة الصور
 * Image Processing Class
 */

class ImageProcessor {
    private $thumbnailsDir;
    private $mediumDir;
    
    public function __construct() {
        $this->thumbnailsDir = ROOT_PATH . '/' . THUMBNAILS_DIR;
        $this->mediumDir = ROOT_PATH . '/' . MEDIUM_DIR;
        
        // التأكد من وجود المجلدات
        if (!is_dir($this->thumbnailsDir)) {
            mkdir($this->thumbnailsDir, 0755, true);
        }
        if (!is_dir($this->mediumDir)) {
            mkdir($this->mediumDir, 0755, true);
        }
    }
    
    /**
     * إنشاء مصغرة للصورة
     */
    public function createThumbnail($sourcePath, $filename) {
        $thumbnailPath = $this->thumbnailsDir . 'thumb_' . $filename;
        
        if ($this->resizeImage($sourcePath, $thumbnailPath, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT, true)) {
            return THUMBNAILS_DIR . 'thumb_' . $filename;
        }
        
        return null;
    }
    
    /**
     * إنشاء صورة متوسطة الحجم
     */
    public function createMediumImage($sourcePath, $filename) {
        $mediumPath = $this->mediumDir . 'medium_' . $filename;
        
        if ($this->resizeImage($sourcePath, $mediumPath, MEDIUM_WIDTH, MEDIUM_HEIGHT, false)) {
            return MEDIUM_DIR . 'medium_' . $filename;
        }
        
        return null;
    }
    
    /**
     * تغيير حجم الصورة
     */
    private function resizeImage($sourcePath, $destPath, $maxWidth, $maxHeight, $crop = false) {
        // الحصول على معلومات الصورة
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) {
            return false;
        }
        
        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $mimeType = $imageInfo['mime'];
        
        // إنشاء مورد الصورة المصدر
        $sourceImage = $this->createImageFromFile($sourcePath, $mimeType);
        if (!$sourceImage) {
            return false;
        }
        
        // حساب الأبعاد الجديدة
        if ($crop) {
            // قص الصورة لتناسب الأبعاد المطلوبة
            $dimensions = $this->calculateCropDimensions($sourceWidth, $sourceHeight, $maxWidth, $maxHeight);
        } else {
            // تغيير الحجم مع الحفاظ على النسبة
            $dimensions = $this->calculateResizeDimensions($sourceWidth, $sourceHeight, $maxWidth, $maxHeight);
        }
        
        // إنشاء الصورة الجديدة
        $newImage = imagecreatetruecolor($dimensions['width'], $dimensions['height']);
        
        // الحفاظ على الشفافية للـ PNG و GIF
        if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefill($newImage, 0, 0, $transparent);
        }
        
        // نسخ وتغيير حجم الصورة
        if ($crop) {
            imagecopyresampled(
                $newImage, $sourceImage,
                0, 0,
                $dimensions['src_x'], $dimensions['src_y'],
                $dimensions['width'], $dimensions['height'],
                $dimensions['src_width'], $dimensions['src_height']
            );
        } else {
            imagecopyresampled(
                $newImage, $sourceImage,
                0, 0, 0, 0,
                $dimensions['width'], $dimensions['height'],
                $sourceWidth, $sourceHeight
            );
        }
        
        // حفظ الصورة
        $result = $this->saveImage($newImage, $destPath, $mimeType);
        
        // تنظيف الذاكرة
        imagedestroy($sourceImage);
        imagedestroy($newImage);
        
        return $result;
    }
    
    /**
     * إنشاء مورد صورة من ملف
     */
    private function createImageFromFile($filePath, $mimeType) {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagecreatefromjpeg($filePath);
            case 'image/png':
                return imagecreatefrompng($filePath);
            case 'image/gif':
                return imagecreatefromgif($filePath);
            case 'image/webp':
                return imagecreatefromwebp($filePath);
            default:
                return false;
        }
    }
    
    /**
     * حفظ الصورة
     */
    private function saveImage($image, $filePath, $mimeType, $quality = 85) {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagejpeg($image, $filePath, $quality);
            case 'image/png':
                return imagepng($image, $filePath, 9);
            case 'image/gif':
                return imagegif($image, $filePath);
            case 'image/webp':
                return imagewebp($image, $filePath, $quality);
            default:
                return false;
        }
    }
    
    /**
     * حساب أبعاد تغيير الحجم
     */
    private function calculateResizeDimensions($sourceWidth, $sourceHeight, $maxWidth, $maxHeight) {
        $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
        
        return [
            'width' => (int)($sourceWidth * $ratio),
            'height' => (int)($sourceHeight * $ratio)
        ];
    }
    
    /**
     * حساب أبعاد القص
     */
    private function calculateCropDimensions($sourceWidth, $sourceHeight, $maxWidth, $maxHeight) {
        $sourceRatio = $sourceWidth / $sourceHeight;
        $targetRatio = $maxWidth / $maxHeight;
        
        if ($sourceRatio > $targetRatio) {
            // الصورة أعرض من المطلوب
            $srcHeight = $sourceHeight;
            $srcWidth = (int)($sourceHeight * $targetRatio);
            $srcX = (int)(($sourceWidth - $srcWidth) / 2);
            $srcY = 0;
        } else {
            // الصورة أطول من المطلوب
            $srcWidth = $sourceWidth;
            $srcHeight = (int)($sourceWidth / $targetRatio);
            $srcX = 0;
            $srcY = (int)(($sourceHeight - $srcHeight) / 2);
        }
        
        return [
            'width' => $maxWidth,
            'height' => $maxHeight,
            'src_x' => $srcX,
            'src_y' => $srcY,
            'src_width' => $srcWidth,
            'src_height' => $srcHeight
        ];
    }
    
    /**
     * استخراج بيانات EXIF
     */
    public function extractExifData($imagePath) {
        if (!function_exists('exif_read_data')) {
            return [];
        }
        
        $exifData = [];
        
        try {
            $exif = exif_read_data($imagePath, 'IFD0,EXIF,GPS', true);
            
            if ($exif) {
                // معلومات الكاميرا
                $exifData['camera_make'] = $exif['IFD0']['Make'] ?? null;
                $exifData['camera_model'] = $exif['IFD0']['Model'] ?? null;
                
                // إعدادات التصوير
                if (isset($exif['EXIF'])) {
                    $exifData['focal_length'] = $this->formatFocalLength($exif['EXIF']['FocalLength'] ?? null);
                    $exifData['aperture'] = $this->formatAperture($exif['EXIF']['FNumber'] ?? null);
                    $exifData['shutter_speed'] = $this->formatShutterSpeed($exif['EXIF']['ExposureTime'] ?? null);
                    $exifData['iso'] = $exif['EXIF']['ISOSpeedRatings'] ?? null;
                    $exifData['flash'] = $this->formatFlash($exif['EXIF']['Flash'] ?? null);
                    $exifData['white_balance'] = $exif['EXIF']['WhiteBalance'] ?? null;
                    $exifData['exposure_mode'] = $exif['EXIF']['ExposureMode'] ?? null;
                    $exifData['metering_mode'] = $exif['EXIF']['MeteringMode'] ?? null;
                    $exifData['lens_model'] = $exif['EXIF']['LensModel'] ?? null;
                    $exifData['color_space'] = $exif['EXIF']['ColorSpace'] ?? null;
                    
                    // تاريخ التصوير
                    if (isset($exif['EXIF']['DateTimeOriginal'])) {
                        $exifData['date_taken'] = date('Y-m-d H:i:s', strtotime($exif['EXIF']['DateTimeOriginal']));
                    }
                }
                
                // معلومات GPS
                if (isset($exif['GPS'])) {
                    $gps = $this->getGPSCoordinates($exif['GPS']);
                    if ($gps) {
                        $exifData['gps_latitude'] = $gps['latitude'];
                        $exifData['gps_longitude'] = $gps['longitude'];
                    }
                }
                
                // اتجاه الصورة
                $exifData['orientation'] = $exif['IFD0']['Orientation'] ?? 1;
            }
        } catch (Exception $e) {
            // تجاهل أخطاء EXIF
        }
        
        return $exifData;
    }
    
    /**
     * تنسيق البعد البؤري
     */
    private function formatFocalLength($focalLength) {
        if (!$focalLength) return null;
        
        if (strpos($focalLength, '/') !== false) {
            $parts = explode('/', $focalLength);
            $focalLength = $parts[0] / $parts[1];
        }
        
        return round($focalLength) . 'mm';
    }
    
    /**
     * تنسيق فتحة العدسة
     */
    private function formatAperture($aperture) {
        if (!$aperture) return null;
        
        if (strpos($aperture, '/') !== false) {
            $parts = explode('/', $aperture);
            $aperture = $parts[0] / $parts[1];
        }
        
        return 'f/' . round($aperture, 1);
    }
    
    /**
     * تنسيق سرعة الغالق
     */
    private function formatShutterSpeed($shutterSpeed) {
        if (!$shutterSpeed) return null;
        
        if (strpos($shutterSpeed, '/') !== false) {
            $parts = explode('/', $shutterSpeed);
            $speed = $parts[0] / $parts[1];
            
            if ($speed < 1) {
                return '1/' . round(1 / $speed) . 's';
            } else {
                return round($speed, 1) . 's';
            }
        }
        
        return $shutterSpeed . 's';
    }
    
    /**
     * تنسيق معلومات الفلاش
     */
    private function formatFlash($flash) {
        if ($flash === null) return null;
        
        $flashModes = [
            0 => 'No Flash',
            1 => 'Flash',
            5 => 'Flash, No Return',
            7 => 'Flash, Return',
            9 => 'Flash, Compulsory',
            13 => 'Flash, Compulsory, No Return',
            15 => 'Flash, Compulsory, Return',
            16 => 'No Flash, Compulsory',
            24 => 'No Flash, Auto',
            25 => 'Flash, Auto',
            29 => 'Flash, Auto, No Return',
            31 => 'Flash, Auto, Return'
        ];
        
        return $flashModes[$flash] ?? 'Unknown';
    }
    
    /**
     * استخراج إحداثيات GPS
     */
    private function getGPSCoordinates($gpsData) {
        if (!isset($gpsData['GPSLatitude']) || !isset($gpsData['GPSLongitude'])) {
            return null;
        }
        
        $latitude = $this->convertGPSCoordinate($gpsData['GPSLatitude'], $gpsData['GPSLatitudeRef']);
        $longitude = $this->convertGPSCoordinate($gpsData['GPSLongitude'], $gpsData['GPSLongitudeRef']);
        
        return [
            'latitude' => $latitude,
            'longitude' => $longitude
        ];
    }
    
    /**
     * تحويل إحداثية GPS
     */
    private function convertGPSCoordinate($coordinate, $hemisphere) {
        if (!is_array($coordinate) || count($coordinate) < 3) {
            return null;
        }
        
        $degrees = $this->gpsToDecimal($coordinate[0]);
        $minutes = $this->gpsToDecimal($coordinate[1]);
        $seconds = $this->gpsToDecimal($coordinate[2]);
        
        $decimal = $degrees + ($minutes / 60) + ($seconds / 3600);
        
        if ($hemisphere === 'S' || $hemisphere === 'W') {
            $decimal *= -1;
        }
        
        return round($decimal, 8);
    }
    
    /**
     * تحويل قيمة GPS إلى عشري
     */
    private function gpsToDecimal($coordinate) {
        if (strpos($coordinate, '/') !== false) {
            $parts = explode('/', $coordinate);
            return $parts[0] / $parts[1];
        }
        return (float)$coordinate;
    }
    
    /**
     * تدوير الصورة حسب EXIF orientation
     */
    public function rotateImageByExif($imagePath) {
        $exif = exif_read_data($imagePath);
        if (!$exif || !isset($exif['Orientation'])) {
            return false;
        }
        
        $orientation = $exif['Orientation'];
        if ($orientation === 1) {
            return true; // لا حاجة للتدوير
        }
        
        $imageInfo = getimagesize($imagePath);
        $image = $this->createImageFromFile($imagePath, $imageInfo['mime']);
        
        if (!$image) {
            return false;
        }
        
        switch ($orientation) {
            case 3:
                $image = imagerotate($image, 180, 0);
                break;
            case 6:
                $image = imagerotate($image, -90, 0);
                break;
            case 8:
                $image = imagerotate($image, 90, 0);
                break;
        }
        
        $result = $this->saveImage($image, $imagePath, $imageInfo['mime']);
        imagedestroy($image);
        
        return $result;
    }
}
?>
