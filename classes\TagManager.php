<?php
/**
 * فئة إدارة العلامات
 * Tag Management Class
 */

class TagManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع العلامات
     */
    public function getTags($includeCount = false) {
        if ($includeCount) {
            $query = "SELECT t.*, COUNT(it.image_id) as usage_count 
                      FROM tags t 
                      LEFT JOIN image_tags it ON t.id = it.tag_id 
                      LEFT JOIN images i ON it.image_id = i.id AND i.is_active = 1
                      GROUP BY t.id 
                      ORDER BY t.name";
        } else {
            $query = "SELECT * FROM tags ORDER BY name";
        }
        
        return $this->db->select($query);
    }
    
    /**
     * الحصول على العلامات مع عدد الاستخدام
     */
    public function getTagsWithUsageCount() {
        return $this->getTags(true);
    }
    
    /**
     * الحصول على علامة واحدة
     */
    public function getTag($id) {
        $query = "SELECT * FROM tags WHERE id = ?";
        return $this->db->selectOne($query, [$id]);
    }
    
    /**
     * الحصول على علامة بالـ slug
     */
    public function getTagBySlug($slug) {
        $query = "SELECT * FROM tags WHERE slug = ?";
        return $this->db->selectOne($query, [$slug]);
    }
    
    /**
     * إضافة علامة جديدة
     */
    public function addTag($data) {
        if (empty($data['name'])) {
            return false;
        }
        
        // التحقق من عدم وجود العلامة
        if ($this->tagExists($data['name'])) {
            return false;
        }
        
        // إنشاء slug فريد
        $slug = $this->db->createUniqueSlug('tags', 'slug', $data['name']);
        
        $query = "INSERT INTO tags (name, name_ar, slug) VALUES (?, ?, ?)";
        
        $params = [
            $data['name'],
            $data['name_ar'] ?? '',
            $slug
        ];
        
        return $this->db->insert($query, $params);
    }
    
    /**
     * تحديث علامة
     */
    public function updateTag($id, $data) {
        $fields = [];
        $params = [];
        
        $allowedFields = ['name', 'name_ar'];
        
        foreach ($allowedFields as $field) {
            if (array_key_exists($field, $data)) {
                $fields[] = "{$field} = ?";
                $params[] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            return false;
        }
        
        // تحديث الـ slug إذا تم تغيير الاسم
        if (isset($data['name'])) {
            $fields[] = "slug = ?";
            $params[] = $this->db->createUniqueSlug('tags', 'slug', $data['name'], $id);
        }
        
        $params[] = $id;
        $query = "UPDATE tags SET " . implode(', ', $fields) . " WHERE id = ?";
        
        return $this->db->update($query, $params);
    }
    
    /**
     * حذف علامة
     */
    public function deleteTag($id) {
        // حذف ربط العلامة بالصور أولاً
        $this->db->delete("DELETE FROM image_tags WHERE tag_id = ?", [$id]);
        
        // حذف العلامة
        $query = "DELETE FROM tags WHERE id = ?";
        return $this->db->delete($query, [$id]);
    }
    
    /**
     * التحقق من وجود علامة بنفس الاسم
     */
    public function tagExists($name, $excludeId = null) {
        $query = "SELECT id FROM tags WHERE name = ?";
        $params = [$name];
        
        if ($excludeId) {
            $query .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        return $this->db->selectOne($query, $params) !== false;
    }
    
    /**
     * البحث في العلامات
     */
    public function searchTags($searchTerm) {
        $searchTerm = '%' . $searchTerm . '%';
        
        $query = "SELECT t.*, COUNT(it.image_id) as usage_count 
                  FROM tags t 
                  LEFT JOIN image_tags it ON t.id = it.tag_id 
                  LEFT JOIN images i ON it.image_id = i.id AND i.is_active = 1
                  WHERE t.name LIKE ? OR t.name_ar LIKE ?
                  GROUP BY t.id 
                  ORDER BY t.name";
        
        return $this->db->select($query, [$searchTerm, $searchTerm]);
    }
    
    /**
     * الحصول على العلامات الأكثر استخداماً
     */
    public function getPopularTags($limit = 20) {
        $query = "SELECT t.*, COUNT(it.image_id) as usage_count 
                  FROM tags t 
                  INNER JOIN image_tags it ON t.id = it.tag_id 
                  INNER JOIN images i ON it.image_id = i.id AND i.is_active = 1
                  GROUP BY t.id 
                  HAVING usage_count > 0
                  ORDER BY usage_count DESC, t.name 
                  LIMIT {$limit}";
        
        return $this->db->select($query);
    }
    
    /**
     * الحصول على عدد استخدام العلامة
     */
    public function getTagUsageCount($tagId) {
        $query = "SELECT COUNT(it.image_id) as count 
                  FROM image_tags it 
                  INNER JOIN images i ON it.image_id = i.id AND i.is_active = 1
                  WHERE it.tag_id = ?";
        
        $result = $this->db->selectOne($query, [$tagId]);
        return $result ? (int)$result['count'] : 0;
    }
    
    /**
     * إنشاء علامات من نص
     */
    public function createTagsFromText($text, $separator = ',') {
        $tagNames = array_map('trim', explode($separator, $text));
        $tagIds = [];
        
        foreach ($tagNames as $tagName) {
            if (empty($tagName)) continue;
            
            // البحث عن العلامة الموجودة
            $existingTag = $this->db->selectOne("SELECT id FROM tags WHERE name = ?", [$tagName]);
            
            if ($existingTag) {
                $tagIds[] = $existingTag['id'];
            } else {
                // إنشاء علامة جديدة
                $tagId = $this->addTag(['name' => $tagName]);
                if ($tagId) {
                    $tagIds[] = $tagId;
                }
            }
        }
        
        return $tagIds;
    }
    
    /**
     * الحصول على اقتراحات العلامات
     */
    public function getTagSuggestions($query, $limit = 10) {
        $searchTerm = '%' . $query . '%';
        
        $sql = "SELECT name, name_ar, slug 
                FROM tags 
                WHERE name LIKE ? OR name_ar LIKE ?
                ORDER BY 
                    CASE WHEN name LIKE ? THEN 1 ELSE 2 END,
                    LENGTH(name),
                    name
                LIMIT {$limit}";
        
        $exactMatch = $query . '%';
        return $this->db->select($sql, [$searchTerm, $searchTerm, $exactMatch]);
    }
    
    /**
     * دمج العلامات
     */
    public function mergeTags($sourceTagId, $targetTagId) {
        if ($sourceTagId === $targetTagId) {
            return false;
        }
        
        $this->db->beginTransaction();
        
        try {
            // نقل جميع ربط الصور من العلامة المصدر إلى العلامة الهدف
            $query = "UPDATE IGNORE image_tags SET tag_id = ? WHERE tag_id = ?";
            $this->db->update($query, [$targetTagId, $sourceTagId]);
            
            // حذف أي ربط مكرر
            $query = "DELETE FROM image_tags WHERE tag_id = ?";
            $this->db->delete($query, [$sourceTagId]);
            
            // حذف العلامة المصدر
            $this->deleteTag($sourceTagId);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * تنظيف العلامات غير المستخدمة
     */
    public function cleanupUnusedTags() {
        $query = "DELETE FROM tags 
                  WHERE id NOT IN (
                      SELECT DISTINCT tag_id 
                      FROM image_tags 
                      WHERE tag_id IS NOT NULL
                  )";
        
        return $this->db->execute($query);
    }
    
    /**
     * الحصول على إحصائيات العلامات
     */
    public function getTagStats() {
        $query = "SELECT 
                    COUNT(*) as total_tags,
                    COUNT(CASE WHEN EXISTS(SELECT 1 FROM image_tags WHERE tag_id = tags.id) THEN 1 END) as used_tags,
                    COUNT(CASE WHEN NOT EXISTS(SELECT 1 FROM image_tags WHERE tag_id = tags.id) THEN 1 END) as unused_tags
                  FROM tags";
        
        return $this->db->selectOne($query);
    }
    
    /**
     * تصدير العلامات
     */
    public function exportTags($format = 'json') {
        $tags = $this->getTagsWithUsageCount();
        
        switch ($format) {
            case 'json':
                return json_encode($tags, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                
            case 'csv':
                $csv = "ID,Name,Name (Arabic),Slug,Usage Count,Created At\n";
                foreach ($tags as $tag) {
                    $csv .= sprintf('"%s","%s","%s","%s","%s","%s"' . "\n",
                        $tag['id'],
                        str_replace('"', '""', $tag['name']),
                        str_replace('"', '""', $tag['name_ar']),
                        $tag['slug'],
                        $tag['usage_count'],
                        $tag['created_at']
                    );
                }
                return $csv;
                
            default:
                return $tags;
        }
    }
}
?>
