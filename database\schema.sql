-- ===================================
-- قاعدة بيانات معرض الصور
-- Photo Gallery Database Schema
-- ===================================

-- إعدادات قاعدة البيانات
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- ===================================
-- جدول الأصناف (Categories)
-- ===================================
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'اسم الصنف بالإنجليزية',
  `name_ar` varchar(100) NOT NULL COMMENT 'اسم الصنف بالعربية',
  `description` text DEFAULT NULL COMMENT 'وصف الصنف بالإنجليزية',
  `description_ar` text DEFAULT NULL COMMENT 'وصف الصنف بالعربية',
  `slug` varchar(120) NOT NULL COMMENT 'الرابط الودود',
  `sort_order` int(11) DEFAULT 0 COMMENT 'ترتيب العرض',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط/غير نشط',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `name_ar` (`name_ar`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_active` (`is_active`),
  KEY `idx_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول أصناف الصور';

-- ===================================
-- جدول العلامات (Tags)
-- ===================================
CREATE TABLE `tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT 'اسم العلامة بالإنجليزية',
  `name_ar` varchar(50) NOT NULL COMMENT 'اسم العلامة بالعربية',
  `slug` varchar(60) NOT NULL COMMENT 'الرابط الودود',
  `description` text DEFAULT NULL COMMENT 'وصف العلامة',
  `color` varchar(7) DEFAULT '#007bff' COMMENT 'لون العلامة',
  `usage_count` int(11) DEFAULT 0 COMMENT 'عدد مرات الاستخدام',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط/غير نشط',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `name_ar` (`name_ar`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_usage` (`usage_count`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول علامات الصور';

-- ===================================
-- جدول الصور (Images)
-- ===================================
CREATE TABLE `images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) NOT NULL COMMENT 'اسم الملف المحفوظ',
  `original_filename` varchar(255) NOT NULL COMMENT 'اسم الملف الأصلي',
  `title` varchar(200) DEFAULT NULL COMMENT 'عنوان الصورة بالإنجليزية',
  `title_ar` varchar(200) DEFAULT NULL COMMENT 'عنوان الصورة بالعربية',
  `description` text DEFAULT NULL COMMENT 'وصف الصورة بالإنجليزية',
  `description_ar` text DEFAULT NULL COMMENT 'وصف الصورة بالعربية',
  `alt_text` varchar(200) DEFAULT NULL COMMENT 'النص البديل بالإنجليزية',
  `alt_text_ar` varchar(200) DEFAULT NULL COMMENT 'النص البديل بالعربية',
  `file_size` bigint(20) NOT NULL COMMENT 'حجم الملف بالبايت',
  `width` int(11) NOT NULL COMMENT 'عرض الصورة',
  `height` int(11) NOT NULL COMMENT 'ارتفاع الصورة',
  `mime_type` varchar(50) NOT NULL COMMENT 'نوع الملف',
  `file_path` varchar(500) NOT NULL COMMENT 'مسار الملف الكامل',
  `thumbnail_path` varchar(500) DEFAULT NULL COMMENT 'مسار الصورة المصغرة',
  `medium_path` varchar(500) DEFAULT NULL COMMENT 'مسار الصورة المتوسطة',
  `category_id` int(11) DEFAULT NULL COMMENT 'معرف الصنف',
  `views_count` int(11) DEFAULT 0 COMMENT 'عدد المشاهدات',
  `downloads_count` int(11) DEFAULT 0 COMMENT 'عدد التحميلات',
  `sort_order` int(11) DEFAULT 0 COMMENT 'ترتيب العرض',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط/غير نشط',
  `is_featured` tinyint(1) DEFAULT 0 COMMENT 'مميز/غير مميز',
  `upload_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `filename` (`filename`),
  KEY `idx_category` (`category_id`),
  KEY `idx_active` (`is_active`),
  KEY `idx_featured` (`is_featured`),
  KEY `idx_upload_date` (`upload_date`),
  KEY `idx_views` (`views_count`),
  KEY `idx_sort` (`sort_order`),
  FULLTEXT KEY `idx_search` (`title`, `title_ar`, `description`, `description_ar`, `alt_text`, `alt_text_ar`),
  CONSTRAINT `fk_images_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الصور الرئيسي';

-- ===================================
-- جدول ربط الصور بالعلامات (Image Tags)
-- ===================================
CREATE TABLE `image_tags` (
  `image_id` int(11) NOT NULL,
  `tag_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`image_id`, `tag_id`),
  KEY `idx_tag` (`tag_id`),
  CONSTRAINT `fk_image_tags_image` FOREIGN KEY (`image_id`) REFERENCES `images` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_image_tags_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول ربط الصور بالعلامات';

-- ===================================
-- جدول بيانات EXIF للصور
-- ===================================
CREATE TABLE `image_exif` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `image_id` int(11) NOT NULL,
  `camera_make` varchar(100) DEFAULT NULL COMMENT 'الشركة المصنعة للكاميرا',
  `camera_model` varchar(100) DEFAULT NULL COMMENT 'موديل الكاميرا',
  `lens_model` varchar(100) DEFAULT NULL COMMENT 'موديل العدسة',
  `focal_length` varchar(20) DEFAULT NULL COMMENT 'البعد البؤري',
  `aperture` varchar(10) DEFAULT NULL COMMENT 'فتحة العدسة',
  `shutter_speed` varchar(20) DEFAULT NULL COMMENT 'سرعة الغالق',
  `iso` varchar(10) DEFAULT NULL COMMENT 'حساسية ISO',
  `flash` varchar(50) DEFAULT NULL COMMENT 'الفلاش',
  `date_taken` datetime DEFAULT NULL COMMENT 'تاريخ التصوير',
  `gps_latitude` decimal(10, 8) DEFAULT NULL COMMENT 'خط العرض',
  `gps_longitude` decimal(11, 8) DEFAULT NULL COMMENT 'خط الطول',
  `orientation` varchar(20) DEFAULT NULL COMMENT 'اتجاه الصورة',
  `color_space` varchar(20) DEFAULT NULL COMMENT 'مساحة الألوان',
  `white_balance` varchar(20) DEFAULT NULL COMMENT 'توازن الأبيض',
  `exposure_mode` varchar(20) DEFAULT NULL COMMENT 'وضع التعرض',
  `metering_mode` varchar(20) DEFAULT NULL COMMENT 'وضع القياس',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `image_id` (`image_id`),
  CONSTRAINT `fk_exif_image` FOREIGN KEY (`image_id`) REFERENCES `images` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول بيانات EXIF للصور';

-- ===================================
-- جدول مستخدمي الإدارة
-- ===================================
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT 'اسم المستخدم',
  `email` varchar(100) NOT NULL COMMENT 'البريد الإلكتروني',
  `password_hash` varchar(255) NOT NULL COMMENT 'كلمة المرور المشفرة',
  `full_name` varchar(100) DEFAULT NULL COMMENT 'الاسم الكامل',
  `remember_token` varchar(255) DEFAULT NULL COMMENT 'رمز تذكر تسجيل الدخول',
  `last_login` timestamp NULL DEFAULT NULL COMMENT 'آخر تسجيل دخول',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط/غير نشط',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_active` (`is_active`),
  KEY `idx_remember_token` (`remember_token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول مستخدمي الإدارة';

-- ===================================
-- جدول محاولات تسجيل الدخول
-- ===================================
CREATE TABLE `login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL COMMENT 'اسم المستخدم',
  `ip_address` varchar(45) NOT NULL COMMENT 'عنوان IP',
  `attempt_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'وقت المحاولة',
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_ip` (`ip_address`),
  KEY `idx_time` (`attempt_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول محاولات تسجيل الدخول';

-- ===================================
-- جدول إعدادات النظام
-- ===================================
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL COMMENT 'مفتاح الإعداد',
  `setting_value` text DEFAULT NULL COMMENT 'قيمة الإعداد',
  `setting_type` enum('string','integer','boolean','json') DEFAULT 'string' COMMENT 'نوع الإعداد',
  `description` varchar(255) DEFAULT NULL COMMENT 'وصف الإعداد',
  `is_public` tinyint(1) DEFAULT 0 COMMENT 'متاح للعرض العام',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول إعدادات النظام';

-- ===================================
-- إدراج البيانات الافتراضية
-- ===================================

-- إدراج مستخدم الإدارة الافتراضي
-- كلمة المرور: admin123
INSERT INTO `admin_users` (`username`, `email`, `password_hash`, `full_name`, `is_active`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 1);

-- إدراج أصناف افتراضية
INSERT INTO `categories` (`name`, `name_ar`, `description`, `description_ar`, `slug`, `sort_order`) VALUES
('Nature', 'الطبيعة', 'Beautiful nature photography', 'صور طبيعية جميلة', 'nature', 1),
('Architecture', 'العمارة', 'Buildings and architectural photography', 'صور المباني والعمارة', 'architecture', 2),
('People', 'الأشخاص', 'Portrait and people photography', 'صور الأشخاص والبورتريه', 'people', 3),
('Travel', 'السفر', 'Travel and tourism photography', 'صور السفر والسياحة', 'travel', 4),
('Abstract', 'التجريد', 'Abstract and artistic photography', 'الصور التجريدية والفنية', 'abstract', 5);

-- إدراج علامات افتراضية
INSERT INTO `tags` (`name`, `name_ar`, `slug`, `description`, `color`) VALUES
('Landscape', 'المناظر الطبيعية', 'landscape', 'Natural landscapes and scenery', '#28a745'),
('Portrait', 'البورتريه', 'portrait', 'People and portrait photography', '#dc3545'),
('Urban', 'حضري', 'urban', 'City and urban photography', '#6c757d'),
('Sunset', 'غروب الشمس', 'sunset', 'Sunset and golden hour photography', '#fd7e14'),
('Black and White', 'أبيض وأسود', 'black-white', 'Monochrome photography', '#000000'),
('Macro', 'ماكرو', 'macro', 'Close-up and macro photography', '#20c997'),
('Street', 'الشارع', 'street', 'Street photography', '#6f42c1'),
('Wildlife', 'الحياة البرية', 'wildlife', 'Animals and wildlife photography', '#198754');

-- إدراج إعدادات افتراضية
INSERT INTO `settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `is_public`) VALUES
('site_name', 'معرض الصور', 'string', 'اسم الموقع', 1),
('site_description', 'معرض صور احترافي متعدد اللغات', 'string', 'وصف الموقع', 1),
('default_language', 'ar', 'string', 'اللغة الافتراضية', 1),
('images_per_page', '12', 'integer', 'عدد الصور في الصفحة الواحدة', 0),
('admin_images_per_page', '20', 'integer', 'عدد الصور في صفحة الإدارة', 0),
('max_file_size', '10485760', 'integer', 'الحد الأقصى لحجم الملف (بايت)', 0),
('allowed_extensions', 'jpg,jpeg,png,gif,webp', 'string', 'امتدادات الملفات المسموحة', 0),
('thumbnail_width', '300', 'integer', 'عرض الصورة المصغرة', 0),
('thumbnail_height', '300', 'integer', 'ارتفاع الصورة المصغرة', 0),
('medium_width', '800', 'integer', 'عرض الصورة المتوسطة', 0),
('medium_height', '600', 'integer', 'ارتفاع الصورة المتوسطة', 0),
('watermark_enabled', '0', 'boolean', 'تفعيل العلامة المائية', 0),
('auto_backup', '1', 'boolean', 'النسخ الاحتياطي التلقائي', 0),
('session_timeout', '3600', 'integer', 'مهلة انتهاء الجلسة (ثانية)', 0),
('max_login_attempts', '5', 'integer', 'عدد محاولات تسجيل الدخول القصوى', 0),
('login_lockout_time', '900', 'integer', 'مدة قفل الحساب (ثانية)', 0);

-- ===================================
-- إنشاء المؤشرات والفهارس الإضافية
-- ===================================

-- فهرس البحث النصي للصور
ALTER TABLE `images` ADD FULLTEXT(`title`, `title_ar`, `description`, `description_ar`);

-- فهرس البحث النصي للأصناف
ALTER TABLE `categories` ADD FULLTEXT(`name`, `name_ar`, `description`, `description_ar`);

-- فهرس البحث النصي للعلامات
ALTER TABLE `tags` ADD FULLTEXT(`name`, `name_ar`, `description`);

-- ===================================
-- إنشاء المشاهدات (Views) للاستعلامات المعقدة
-- ===================================

-- مشاهدة الصور مع تفاصيل الأصناف والعلامات
CREATE VIEW `v_images_detailed` AS
SELECT 
    i.*,
    c.name as category_name,
    c.name_ar as category_name_ar,
    c.slug as category_slug,
    GROUP_CONCAT(t.name SEPARATOR ', ') as tags,
    GROUP_CONCAT(t.name_ar SEPARATOR '، ') as tags_ar,
    COUNT(DISTINCT it.tag_id) as tags_count
FROM `images` i
LEFT JOIN `categories` c ON i.category_id = c.id
LEFT JOIN `image_tags` it ON i.id = it.image_id
LEFT JOIN `tags` t ON it.tag_id = t.id
WHERE i.is_active = 1
GROUP BY i.id;

-- مشاهدة الأصناف مع عدد الصور
CREATE VIEW `v_categories_with_counts` AS
SELECT 
    c.*,
    COUNT(i.id) as image_count,
    SUM(i.views_count) as total_views
FROM `categories` c
LEFT JOIN `images` i ON c.id = i.category_id AND i.is_active = 1
GROUP BY c.id;

-- مشاهدة العلامات مع عدد الاستخدام
CREATE VIEW `v_tags_with_usage` AS
SELECT 
    t.*,
    COUNT(it.image_id) as actual_usage_count
FROM `tags` t
LEFT JOIN `image_tags` it ON t.id = it.tag_id
LEFT JOIN `images` i ON it.image_id = i.id AND i.is_active = 1
GROUP BY t.id;

-- ===================================
-- إنشاء المحفزات (Triggers) لتحديث العدادات
-- ===================================

-- محفز تحديث عداد استخدام العلامات عند إضافة ربط
DELIMITER $$
CREATE TRIGGER `update_tag_usage_on_insert` 
AFTER INSERT ON `image_tags`
FOR EACH ROW
BEGIN
    UPDATE `tags` 
    SET `usage_count` = (
        SELECT COUNT(*) 
        FROM `image_tags` it 
        JOIN `images` i ON it.image_id = i.id 
        WHERE it.tag_id = NEW.tag_id AND i.is_active = 1
    )
    WHERE `id` = NEW.tag_id;
END$$

-- محفز تحديث عداد استخدام العلامات عند حذف ربط
CREATE TRIGGER `update_tag_usage_on_delete` 
AFTER DELETE ON `image_tags`
FOR EACH ROW
BEGIN
    UPDATE `tags` 
    SET `usage_count` = (
        SELECT COUNT(*) 
        FROM `image_tags` it 
        JOIN `images` i ON it.image_id = i.id 
        WHERE it.tag_id = OLD.tag_id AND i.is_active = 1
    )
    WHERE `id` = OLD.tag_id;
END$$

DELIMITER ;

-- ===================================
-- إنشاء الإجراءات المخزنة (Stored Procedures)
-- ===================================

-- إجراء تنظيف البيانات القديمة
DELIMITER $$
CREATE PROCEDURE `CleanupOldData`()
BEGIN
    -- حذف محاولات تسجيل الدخول القديمة (أكثر من 30 يوم)
    DELETE FROM `login_attempts` 
    WHERE `attempt_time` < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- تحديث عدادات استخدام العلامات
    UPDATE `tags` t 
    SET `usage_count` = (
        SELECT COUNT(*) 
        FROM `image_tags` it 
        JOIN `images` i ON it.image_id = i.id 
        WHERE it.tag_id = t.id AND i.is_active = 1
    );
    
    -- حذف العلامات غير المستخدمة (اختياري)
    -- DELETE FROM `tags` WHERE `usage_count` = 0 AND `created_at` < DATE_SUB(NOW(), INTERVAL 90 DAY);
END$$

DELIMITER ;

-- ===================================
-- إنشاء فهارس إضافية للأداء
-- ===================================

-- فهارس مركبة للاستعلامات الشائعة
CREATE INDEX `idx_images_category_active` ON `images` (`category_id`, `is_active`, `upload_date`);
CREATE INDEX `idx_images_featured_active` ON `images` (`is_featured`, `is_active`, `views_count`);
CREATE INDEX `idx_images_date_views` ON `images` (`upload_date`, `views_count`);

-- فهارس للبحث والتصفية
CREATE INDEX `idx_categories_active_sort` ON `categories` (`is_active`, `sort_order`);
CREATE INDEX `idx_tags_active_usage` ON `tags` (`is_active`, `usage_count`);

COMMIT;
