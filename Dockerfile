# ===================================
# Dockerfile لمعرض الصور
# Photo Gallery Dockerfile
# ===================================

FROM php:8.2-apache

# تثبيت التبعيات النظام
RUN apt-get update && apt-get install -y \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    libwebp-dev \
    libxpm-dev \
    libzip-dev \
    libicu-dev \
    libonig-dev \
    libxml2-dev \
    libcurl4-openssl-dev \
    libssl-dev \
    unzip \
    git \
    curl \
    nano \
    && rm -rf /var/lib/apt/lists/*

# تثبيت امتدادات PHP
RUN docker-php-ext-configure gd \
    --with-freetype \
    --with-jpeg \
    --with-webp \
    --with-xpm \
    && docker-php-ext-install -j$(nproc) \
    gd \
    pdo \
    pdo_mysql \
    mysqli \
    zip \
    intl \
    mbstring \
    xml \
    curl \
    exif \
    opcache

# تثبيت Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# تفعيل mod_rewrite
RUN a2enmod rewrite headers expires deflate

# إعداد PHP
COPY docker/php/php.ini /usr/local/etc/php/conf.d/custom.ini
COPY docker/php/opcache.ini /usr/local/etc/php/conf.d/opcache.ini

# إعداد Apache
COPY docker/apache/000-default.conf /etc/apache2/sites-available/000-default.conf
COPY docker/apache/apache2.conf /etc/apache2/apache2.conf

# تعيين مجلد العمل
WORKDIR /var/www/html

# نسخ ملفات المشروع
COPY . .

# إنشاء المجلدات المطلوبة
RUN mkdir -p uploads/thumbnails uploads/medium uploads/temp logs cache \
    && chown -R www-data:www-data uploads/ logs/ cache/ \
    && chmod -R 755 uploads/ \
    && chmod -R 644 logs/ \
    && chmod -R 755 cache/

# تثبيت تبعيات Composer (إذا كان ملف composer.json موجود)
RUN if [ -f composer.json ]; then \
        composer install --no-dev --optimize-autoloader; \
    fi

# إعداد متغيرات البيئة
ENV APACHE_DOCUMENT_ROOT=/var/www/html
ENV APACHE_LOG_DIR=/var/log/apache2
ENV PHP_MEMORY_LIMIT=256M
ENV PHP_UPLOAD_MAX_FILESIZE=10M
ENV PHP_POST_MAX_SIZE=50M
ENV PHP_MAX_EXECUTION_TIME=300

# تعريض المنافذ
EXPOSE 80

# نقطة الدخول
CMD ["apache2-foreground"]

# إضافة معلومات الصورة
LABEL maintainer="<EMAIL>"
LABEL description="معرض صور احترافي متعدد اللغات"
LABEL version="1.0.0"
LABEL org.opencontainers.image.title="Multilingual Photo Gallery"
LABEL org.opencontainers.image.description="Professional photo gallery with Arabic and English support"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="Your Company"
LABEL org.opencontainers.image.licenses="MIT"
