<?php
/**
 * معالج رفع الصور عبر AJAX
 * AJAX Image Upload Handler
 */

require_once '../../init.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح بالوصول']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit;
}

// التحقق من وجود الملف
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'لم يتم رفع ملف صحيح']);
    exit;
}

try {
    // إنشاء كائنات الإدارة
    $imageManager = new ImageManager();
    $categoryManager = new CategoryManager();
    $tagManager = new TagManager();
    $fileUploader = new FileUploader();
    $imageProcessor = new ImageProcessor();
    
    // رفع الصورة
    $result = $fileUploader->uploadImage($_FILES['image']);
    
    if (!$result) {
        $errors = $fileUploader->getErrors();
        echo json_encode([
            'success' => false, 
            'message' => !empty($errors) ? implode(', ', $errors) : 'فشل في رفع الصورة'
        ]);
        exit;
    }
    
    // إعداد بيانات الصورة
    $imageData = [
        'filename' => $result['filename'],
        'original_filename' => $result['original_filename'],
        'title' => $_POST['title'] ?? '',
        'title_ar' => $_POST['title_ar'] ?? '',
        'description' => $_POST['description'] ?? '',
        'description_ar' => $_POST['description_ar'] ?? '',
        'alt_text' => $_POST['alt_text'] ?? '',
        'alt_text_ar' => $_POST['alt_text_ar'] ?? '',
        'file_size' => $result['file_size'],
        'width' => $result['width'],
        'height' => $result['height'],
        'mime_type' => $result['mime_type'],
        'file_path' => $result['file_path'],
        'thumbnail_path' => $result['thumbnail_path'],
        'medium_path' => $result['medium_path'],
        'category_id' => !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
        'sort_order' => (int)($_POST['sort_order'] ?? 0)
    ];
    
    // إضافة الصورة إلى قاعدة البيانات
    $imageId = $imageManager->addImage($imageData);
    
    if (!$imageId) {
        // حذف الملفات المرفوعة في حالة فشل إضافة السجل
        $fileUploader->deleteFile($result['filename']);
        if ($result['thumbnail_path']) {
            unlink(ROOT_PATH . '/' . $result['thumbnail_path']);
        }
        if ($result['medium_path']) {
            unlink(ROOT_PATH . '/' . $result['medium_path']);
        }
        
        echo json_encode(['success' => false, 'message' => 'فشل في حفظ بيانات الصورة']);
        exit;
    }
    
    // إضافة بيانات EXIF
    if (!empty($result['exif_data'])) {
        $exifData = $result['exif_data'];
        
        $exifQuery = "INSERT INTO image_exif (image_id, camera_make, camera_model, lens_model, 
                      focal_length, aperture, shutter_speed, iso, flash, date_taken, 
                      gps_latitude, gps_longitude, orientation, color_space, white_balance, 
                      exposure_mode, metering_mode) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $db->insert($exifQuery, [
            $imageId,
            $exifData['camera_make'] ?? null,
            $exifData['camera_model'] ?? null,
            $exifData['lens_model'] ?? null,
            $exifData['focal_length'] ?? null,
            $exifData['aperture'] ?? null,
            $exifData['shutter_speed'] ?? null,
            $exifData['iso'] ?? null,
            $exifData['flash'] ?? null,
            $exifData['date_taken'] ?? null,
            $exifData['gps_latitude'] ?? null,
            $exifData['gps_longitude'] ?? null,
            $exifData['orientation'] ?? null,
            $exifData['color_space'] ?? null,
            $exifData['white_balance'] ?? null,
            $exifData['exposure_mode'] ?? null,
            $exifData['metering_mode'] ?? null
        ]);
    }
    
    // إضافة العلامات
    if (!empty($_POST['tags'])) {
        $tagIds = $tagManager->createTagsFromText($_POST['tags']);
        if (!empty($tagIds)) {
            $imageManager->addImageTags($imageId, $tagIds);
        }
    }
    
    // الحصول على بيانات الصورة المحدثة
    $uploadedImage = $imageManager->getImage($imageId);
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'message' => 'تم رفع الصورة بنجاح',
        'image_id' => $imageId,
        'image_data' => $uploadedImage,
        'thumbnail_url' => THUMBNAILS_URL . basename($result['thumbnail_path']),
        'medium_url' => MEDIUM_URL . basename($result['medium_path']),
        'full_url' => UPLOADS_URL . $result['file_path']
    ]);
    
} catch (Exception $e) {
    // تسجيل الخطأ
    error_log("Upload error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => DEBUG_MODE ? $e->getMessage() : 'حدث خطأ أثناء رفع الصورة'
    ]);
}
?>
