/**
 * ملف JavaScript لرفع الصور
 * Image Upload JavaScript
 */

(function() {
    'use strict';

    let dropzone;
    let uploadedFiles = [];
    
    // تهيئة Dropzone عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        initializeDropzone();
        initializeFormHandlers();
        initializeProgressTracking();
    });

    // تهيئة Dropzone
    function initializeDropzone() {
        // التحقق من دعم Dropzone
        if (typeof Dropzone === 'undefined') {
            console.warn('Dropzone not loaded, falling back to traditional upload');
            showTraditionalUpload();
            return;
        }

        // إعدادات Dropzone
        Dropzone.autoDiscover = false;
        
        const dropzoneElement = document.getElementById('image-dropzone');
        if (!dropzoneElement) return;

        dropzone = new Dropzone(dropzoneElement, {
            url: 'ajax/upload-handler.php',
            paramName: 'image',
            maxFilesize: <?php echo MAX_FILE_SIZE / (1024 * 1024); ?>, // MB
            acceptedFiles: 'image/*',
            addRemoveLinks: true,
            dictDefaultMessage: '',
            dictRemoveFile: '<?php echo __('remove'); ?>',
            dictCancelUpload: '<?php echo __('cancel'); ?>',
            dictUploadCanceled: '<?php echo __('upload_cancelled'); ?>',
            dictInvalidFileType: '<?php echo __('invalid_file_type'); ?>',
            dictFileTooBig: '<?php echo __('file_too_big'); ?>',
            dictResponseError: '<?php echo __('upload_error'); ?>',
            parallelUploads: 3,
            uploadMultiple: false,
            autoProcessQueue: false,
            previewTemplate: getPreviewTemplate(),
            
            init: function() {
                const dz = this;
                
                // إضافة زر الرفع
                const uploadButton = document.createElement('button');
                uploadButton.type = 'button';
                uploadButton.className = 'btn btn-primary btn-lg mt-3';
                uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i><?php echo __('start_upload'); ?>';
                uploadButton.addEventListener('click', function() {
                    if (dz.getQueuedFiles().length > 0) {
                        startUpload();
                    } else {
                        alert('<?php echo __('please_select_files'); ?>');
                    }
                });
                
                dropzoneElement.appendChild(uploadButton);
                
                // معالجات الأحداث
                dz.on('addedfile', function(file) {
                    console.log('File added:', file.name);
                    updateUploadButton();
                });
                
                dz.on('removedfile', function(file) {
                    console.log('File removed:', file.name);
                    updateUploadButton();
                });
                
                dz.on('sending', function(file, xhr, formData) {
                    // إضافة بيانات النموذج
                    const formElements = document.getElementById('imageDetailsForm').elements;
                    for (let element of formElements) {
                        if (element.name && element.value) {
                            formData.append(element.name, element.value);
                        }
                    }
                });
                
                dz.on('success', function(file, response) {
                    try {
                        const result = JSON.parse(response);
                        if (result.success) {
                            file.serverId = result.image_id;
                            uploadedFiles.push(result);
                            showUploadSuccess(file, result);
                        } else {
                            showUploadError(file, result.message);
                        }
                    } catch (e) {
                        showUploadError(file, '<?php echo __('invalid_response'); ?>');
                    }
                });
                
                dz.on('error', function(file, errorMessage) {
                    showUploadError(file, errorMessage);
                });
                
                dz.on('queuecomplete', function() {
                    console.log('All uploads completed');
                    showUploadComplete();
                });
                
                dz.on('totaluploadprogress', function(progress) {
                    updateProgressBar(progress);
                });
            }
        });
    }

    // قالب معاينة الملف
    function getPreviewTemplate() {
        return `
            <div class="dz-preview dz-file-preview">
                <div class="dz-image">
                    <img data-dz-thumbnail />
                </div>
                <div class="dz-details">
                    <div class="dz-size"><span data-dz-size></span></div>
                    <div class="dz-filename"><span data-dz-name></span></div>
                </div>
                <div class="dz-progress">
                    <span class="dz-upload" data-dz-uploadprogress></span>
                </div>
                <div class="dz-error-message"><span data-dz-errormessage></span></div>
                <div class="dz-success-mark">
                    <i class="fas fa-check-circle text-success"></i>
                </div>
                <div class="dz-error-mark">
                    <i class="fas fa-times-circle text-danger"></i>
                </div>
                <div class="dz-remove" data-dz-remove>
                    <i class="fas fa-trash"></i>
                </div>
            </div>
        `;
    }

    // بدء الرفع
    function startUpload() {
        if (!dropzone) return;
        
        showProgressCard();
        dropzone.processQueue();
    }

    // تحديث زر الرفع
    function updateUploadButton() {
        const uploadButton = document.querySelector('#image-dropzone .btn-primary');
        if (!uploadButton || !dropzone) return;
        
        const queuedFiles = dropzone.getQueuedFiles().length;
        
        if (queuedFiles > 0) {
            uploadButton.innerHTML = `<i class="fas fa-upload me-2"></i><?php echo __('upload'); ?> (${queuedFiles})`;
            uploadButton.disabled = false;
        } else {
            uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i><?php echo __('start_upload'); ?>';
            uploadButton.disabled = true;
        }
    }

    // عرض بطاقة التقدم
    function showProgressCard() {
        const progressCard = document.getElementById('upload-progress');
        if (progressCard) {
            progressCard.style.display = 'block';
        }
    }

    // تحديث شريط التقدم
    function updateProgressBar(progress) {
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        const progressFiles = document.getElementById('progress-files');
        
        if (progressBar) {
            progressBar.style.width = progress + '%';
        }
        
        if (progressText) {
            progressText.textContent = Math.round(progress) + '%';
        }
        
        if (progressFiles && dropzone) {
            const completed = dropzone.getUploadingFiles().length + dropzone.getAcceptedFiles().length - dropzone.getQueuedFiles().length;
            const total = dropzone.getAcceptedFiles().length;
            progressFiles.textContent = `${completed} / ${total}`;
        }
    }

    // عرض نجاح الرفع
    function showUploadSuccess(file, result) {
        console.log('Upload successful:', result);
        
        // إضافة علامة النجاح
        const successMark = file.previewElement.querySelector('.dz-success-mark');
        if (successMark) {
            successMark.style.display = 'block';
        }
    }

    // عرض خطأ الرفع
    function showUploadError(file, errorMessage) {
        console.error('Upload error:', errorMessage);
        
        // إضافة علامة الخطأ
        const errorMark = file.previewElement.querySelector('.dz-error-mark');
        if (errorMark) {
            errorMark.style.display = 'block';
        }
        
        // عرض رسالة الخطأ
        const errorElement = file.previewElement.querySelector('.dz-error-message span');
        if (errorElement) {
            errorElement.textContent = errorMessage;
        }
    }

    // عرض اكتمال الرفع
    function showUploadComplete() {
        if (uploadedFiles.length > 0) {
            const message = `<?php echo __('upload_completed'); ?>! ${uploadedFiles.length} <?php echo __('images_uploaded'); ?>`;
            showNotification(message, 'success');
            
            // إعادة توجيه إلى صفحة الصور بعد 3 ثوان
            setTimeout(function() {
                window.location.href = 'images.php';
            }, 3000);
        }
    }

    // عرض الرفع التقليدي
    function showTraditionalUpload() {
        const dropzoneElement = document.getElementById('image-dropzone');
        const traditionalUpload = document.getElementById('traditional-upload');
        
        if (dropzoneElement) {
            dropzoneElement.style.display = 'none';
        }
        
        if (traditionalUpload) {
            traditionalUpload.style.display = 'block';
        }
    }

    // تهيئة معالجات النماذج
    function initializeFormHandlers() {
        // نموذج التفاصيل
        const detailsForm = document.getElementById('imageDetailsForm');
        if (detailsForm) {
            // حفظ البيانات في localStorage
            const inputs = detailsForm.querySelectorAll('input, textarea, select');
            inputs.forEach(function(input) {
                input.addEventListener('change', function() {
                    localStorage.setItem('upload_' + this.name, this.value);
                });
                
                // استرداد البيانات المحفوظة
                const savedValue = localStorage.getItem('upload_' + input.name);
                if (savedValue) {
                    input.value = savedValue;
                }
            });
        }

        // النموذج التقليدي
        const uploadForm = document.getElementById('uploadForm');
        if (uploadForm) {
            uploadForm.addEventListener('submit', function(e) {
                const fileInput = document.getElementById('images');
                if (!fileInput.files.length) {
                    e.preventDefault();
                    alert('<?php echo __('please_select_files'); ?>');
                    return false;
                }
                
                // عرض حالة التحميل
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i><?php echo __('uploading'); ?>...';
                submitBtn.disabled = true;
            });
        }
    }

    // تهيئة تتبع التقدم
    function initializeProgressTracking() {
        // مراقبة تقدم الرفع التقليدي
        const uploadForm = document.getElementById('uploadForm');
        if (uploadForm) {
            uploadForm.addEventListener('submit', function() {
                showProgressCard();
                simulateProgress();
            });
        }
    }

    // محاكاة التقدم للرفع التقليدي
    function simulateProgress() {
        let progress = 0;
        const interval = setInterval(function() {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            
            updateProgressBar(progress);
            
            if (progress >= 90) {
                clearInterval(interval);
            }
        }, 500);
    }

    // عرض الإشعارات
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // إزالة الإشعار تلقائياً
        setTimeout(function() {
            if (notification.parentNode) {
                notification.classList.remove('show');
                setTimeout(function() {
                    if (notification.parentNode) {
                        document.body.removeChild(notification);
                    }
                }, 150);
            }
        }, 5000);
    }

    // تنظيف البيانات المحفوظة
    function clearSavedData() {
        const keys = Object.keys(localStorage);
        keys.forEach(function(key) {
            if (key.startsWith('upload_')) {
                localStorage.removeItem(key);
            }
        });
    }

    // معاينة الصور قبل الرفع
    function previewImages(files) {
        const previewContainer = document.getElementById('image-previews');
        if (!previewContainer) return;
        
        previewContainer.innerHTML = '';
        
        Array.from(files).forEach(function(file, index) {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.createElement('div');
                    preview.className = 'col-md-3 mb-3';
                    preview.innerHTML = `
                        <div class="card">
                            <img src="${e.target.result}" class="card-img-top" style="height: 150px; object-fit: cover;">
                            <div class="card-body p-2">
                                <small class="text-muted">${file.name}</small>
                                <br>
                                <small class="text-muted">${formatFileSize(file.size)}</small>
                            </div>
                        </div>
                    `;
                    previewContainer.appendChild(preview);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // تنسيق حجم الملف
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // التحقق من صحة الملفات
    function validateFiles(files) {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        const maxSize = <?php echo MAX_FILE_SIZE; ?>;
        const errors = [];
        
        Array.from(files).forEach(function(file) {
            if (!allowedTypes.includes(file.type)) {
                errors.push(`${file.name}: <?php echo __('invalid_file_type'); ?>`);
            }
            
            if (file.size > maxSize) {
                errors.push(`${file.name}: <?php echo __('file_too_large'); ?>`);
            }
        });
        
        return errors;
    }

    // معالجة السحب والإفلات للنموذج التقليدي
    function initializeDragDrop() {
        const uploadArea = document.getElementById('traditional-upload');
        if (!uploadArea) return;
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight(e) {
            uploadArea.classList.add('drag-over');
        }
        
        function unhighlight(e) {
            uploadArea.classList.remove('drag-over');
        }
        
        uploadArea.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            
            const fileInput = document.getElementById('images');
            if (fileInput) {
                fileInput.files = files;
                previewImages(files);
            }
        }
    }

    // تصدير الدوال للاستخدام العام
    window.UploadManager = {
        startUpload: startUpload,
        clearSavedData: clearSavedData,
        showNotification: showNotification,
        validateFiles: validateFiles
    };

})();
