<?php
/**
 * فئة التصفح (Pagination)
 * Pagination Class
 */

class Pagination {
    private $totalItems;
    private $itemsPerPage;
    private $currentPage;
    private $totalPages;
    private $baseUrl;
    private $queryParams;
    
    public function __construct($totalItems, $itemsPerPage = 10, $currentPage = 1, $baseUrl = '', $queryParams = []) {
        $this->totalItems = (int)$totalItems;
        $this->itemsPerPage = (int)$itemsPerPage;
        $this->currentPage = max(1, (int)$currentPage);
        $this->totalPages = ceil($this->totalItems / $this->itemsPerPage);
        $this->baseUrl = $baseUrl;
        $this->queryParams = $queryParams;
        
        // التأكد من أن الصفحة الحالية لا تتجاوز العدد الكلي
        if ($this->currentPage > $this->totalPages && $this->totalPages > 0) {
            $this->currentPage = $this->totalPages;
        }
    }
    
    /**
     * الحصول على الصفحة الحالية
     */
    public function getCurrentPage() {
        return $this->currentPage;
    }
    
    /**
     * الحصول على العدد الكلي للصفحات
     */
    public function getTotalPages() {
        return $this->totalPages;
    }
    
    /**
     * الحصول على العدد الكلي للعناصر
     */
    public function getTotalItems() {
        return $this->totalItems;
    }
    
    /**
     * الحصول على عدد العناصر في الصفحة
     */
    public function getItemsPerPage() {
        return $this->itemsPerPage;
    }
    
    /**
     * الحصول على رقم العنصر الأول في الصفحة الحالية
     */
    public function getStartItem() {
        return ($this->currentPage - 1) * $this->itemsPerPage + 1;
    }
    
    /**
     * الحصول على رقم العنصر الأخير في الصفحة الحالية
     */
    public function getEndItem() {
        $endItem = $this->currentPage * $this->itemsPerPage;
        return min($endItem, $this->totalItems);
    }
    
    /**
     * الحصول على OFFSET لاستعلام قاعدة البيانات
     */
    public function getOffset() {
        return ($this->currentPage - 1) * $this->itemsPerPage;
    }
    
    /**
     * التحقق من وجود صفحة سابقة
     */
    public function hasPrevious() {
        return $this->currentPage > 1;
    }
    
    /**
     * التحقق من وجود صفحة تالية
     */
    public function hasNext() {
        return $this->currentPage < $this->totalPages;
    }
    
    /**
     * الحصول على رقم الصفحة السابقة
     */
    public function getPreviousPage() {
        return $this->hasPrevious() ? $this->currentPage - 1 : null;
    }
    
    /**
     * الحصول على رقم الصفحة التالية
     */
    public function getNextPage() {
        return $this->hasNext() ? $this->currentPage + 1 : null;
    }
    
    /**
     * إنشاء رابط للصفحة
     */
    public function createPageUrl($page) {
        $params = $this->queryParams;
        $params['page'] = $page;
        
        $url = $this->baseUrl;
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }
    
    /**
     * الحصول على أرقام الصفحات للعرض
     */
    public function getPageNumbers($range = 5) {
        $pages = [];
        $start = max(1, $this->currentPage - floor($range / 2));
        $end = min($this->totalPages, $start + $range - 1);
        
        // تعديل البداية إذا كانت النهاية قريبة من آخر صفحة
        if ($end - $start + 1 < $range) {
            $start = max(1, $end - $range + 1);
        }
        
        for ($i = $start; $i <= $end; $i++) {
            $pages[] = $i;
        }
        
        return $pages;
    }
    
    /**
     * إنشاء HTML للتصفح
     */
    public function render($options = []) {
        if ($this->totalPages <= 1) {
            return '';
        }
        
        $lang = getCurrentLanguage();
        $isRTL = isRTL($lang);
        
        // النصوص
        $texts = [
            'ar' => [
                'previous' => 'السابق',
                'next' => 'التالي',
                'first' => 'الأولى',
                'last' => 'الأخيرة',
                'showing' => 'عرض',
                'to' => 'إلى',
                'of' => 'من',
                'results' => 'نتيجة'
            ],
            'en' => [
                'previous' => 'Previous',
                'next' => 'Next',
                'first' => 'First',
                'last' => 'Last',
                'showing' => 'Showing',
                'to' => 'to',
                'of' => 'of',
                'results' => 'results'
            ]
        ];
        
        $text = $texts[$lang] ?? $texts['en'];
        
        // الخيارات الافتراضية
        $defaultOptions = [
            'show_info' => true,
            'show_first_last' => true,
            'range' => 5,
            'class' => 'pagination justify-content-center',
            'size' => '' // sm, lg
        ];
        
        $options = array_merge($defaultOptions, $options);
        
        $html = '<nav aria-label="Page navigation">';
        
        // معلومات النتائج
        if ($options['show_info']) {
            $html .= '<div class="pagination-info mb-3 text-center text-muted">';
            $html .= $text['showing'] . ' ' . $this->getStartItem() . ' ' . $text['to'] . ' ' . 
                     $this->getEndItem() . ' ' . $text['of'] . ' ' . $this->totalItems . ' ' . $text['results'];
            $html .= '</div>';
        }
        
        $html .= '<ul class="' . $options['class'];
        if ($options['size']) {
            $html .= ' pagination-' . $options['size'];
        }
        $html .= '">';
        
        // رابط الصفحة الأولى
        if ($options['show_first_last'] && $this->currentPage > 1) {
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . $this->createPageUrl(1) . '">' . $text['first'] . '</a>';
            $html .= '</li>';
        }
        
        // رابط الصفحة السابقة
        if ($this->hasPrevious()) {
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . $this->createPageUrl($this->getPreviousPage()) . '">';
            $html .= '<span aria-hidden="true">' . ($isRTL ? '&raquo;' : '&laquo;') . '</span> ' . $text['previous'];
            $html .= '</a></li>';
        }
        
        // أرقام الصفحات
        $pageNumbers = $this->getPageNumbers($options['range']);
        
        foreach ($pageNumbers as $pageNum) {
            $isActive = ($pageNum == $this->currentPage);
            $html .= '<li class="page-item' . ($isActive ? ' active' : '') . '">';
            
            if ($isActive) {
                $html .= '<span class="page-link">' . $pageNum . '</span>';
            } else {
                $html .= '<a class="page-link" href="' . $this->createPageUrl($pageNum) . '">' . $pageNum . '</a>';
            }
            
            $html .= '</li>';
        }
        
        // رابط الصفحة التالية
        if ($this->hasNext()) {
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . $this->createPageUrl($this->getNextPage()) . '">';
            $html .= $text['next'] . ' <span aria-hidden="true">' . ($isRTL ? '&laquo;' : '&raquo;') . '</span>';
            $html .= '</a></li>';
        }
        
        // رابط الصفحة الأخيرة
        if ($options['show_first_last'] && $this->currentPage < $this->totalPages) {
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . $this->createPageUrl($this->totalPages) . '">' . $text['last'] . '</a>';
            $html .= '</li>';
        }
        
        $html .= '</ul></nav>';
        
        return $html;
    }
    
    /**
     * إنشاء تصفح مبسط (السابق/التالي فقط)
     */
    public function renderSimple($options = []) {
        if ($this->totalPages <= 1) {
            return '';
        }
        
        $lang = getCurrentLanguage();
        $isRTL = isRTL($lang);
        
        $texts = [
            'ar' => ['previous' => 'السابق', 'next' => 'التالي'],
            'en' => ['previous' => 'Previous', 'next' => 'Next']
        ];
        
        $text = $texts[$lang] ?? $texts['en'];
        
        $html = '<nav aria-label="Page navigation">';
        $html .= '<ul class="pagination pagination-simple justify-content-between">';
        
        // الصفحة السابقة
        if ($this->hasPrevious()) {
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . $this->createPageUrl($this->getPreviousPage()) . '">';
            $html .= '<span aria-hidden="true">' . ($isRTL ? '&raquo;' : '&laquo;') . '</span> ' . $text['previous'];
            $html .= '</a></li>';
        } else {
            $html .= '<li class="page-item disabled"><span class="page-link">' . $text['previous'] . '</span></li>';
        }
        
        // الصفحة التالية
        if ($this->hasNext()) {
            $html .= '<li class="page-item">';
            $html .= '<a class="page-link" href="' . $this->createPageUrl($this->getNextPage()) . '">';
            $html .= $text['next'] . ' <span aria-hidden="true">' . ($isRTL ? '&laquo;' : '&raquo;') . '</span>';
            $html .= '</a></li>';
        } else {
            $html .= '<li class="page-item disabled"><span class="page-link">' . $text['next'] . '</span></li>';
        }
        
        $html .= '</ul></nav>';
        
        return $html;
    }
    
    /**
     * الحصول على معلومات التصفح كمصفوفة
     */
    public function toArray() {
        return [
            'current_page' => $this->currentPage,
            'total_pages' => $this->totalPages,
            'total_items' => $this->totalItems,
            'items_per_page' => $this->itemsPerPage,
            'start_item' => $this->getStartItem(),
            'end_item' => $this->getEndItem(),
            'has_previous' => $this->hasPrevious(),
            'has_next' => $this->hasNext(),
            'previous_page' => $this->getPreviousPage(),
            'next_page' => $this->getNextPage(),
            'page_numbers' => $this->getPageNumbers()
        ];
    }
}
?>
