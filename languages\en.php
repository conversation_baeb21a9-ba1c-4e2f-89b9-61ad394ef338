<?php
/**
 * ملف ترجمة اللغة الإنجليزية
 * English Language File
 */

return [
    // General
    'home' => 'Home',
    'gallery' => 'Gallery',
    'categories' => 'Categories',
    'tags' => 'Tags',
    'search' => 'Search',
    'admin' => 'Admin',
    'login' => 'Login',
    'logout' => 'Logout',
    'upload' => 'Upload',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'loading' => 'Loading...',
    'no_results' => 'No results found',
    'view_all' => 'View All',
    'show_more' => 'Show More',
    'close' => 'Close',
    'confirm' => 'Confirm',
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Info',
    'yes' => 'Yes',
    'no' => 'No',
    'english' => 'English',
    'arabic' => 'Arabic',

    // Images
    'images' => 'Images',
    'image' => 'Image',
    'title' => 'Title',
    'description' => 'Description',
    'category' => 'Category',
    'upload_date' => 'Upload Date',
    'file_size' => 'File Size',
    'dimensions' => 'Dimensions',
    'views' => 'Views',
    'download' => 'Download',
    'share' => 'Share',
    'fullscreen' => 'Fullscreen',
    'slideshow' => 'Slideshow',
    'thumbnail' => 'Thumbnail',
    'alt_text' => 'Alt Text',
    'original_filename' => 'Original Filename',
    'mime_type' => 'File Type',

    // Categories
    'all_categories' => 'All Categories',
    'category_name' => 'Category Name',
    'category_description' => 'Category Description',
    'images_count' => 'Images Count',
    'add_category' => 'Add Category',
    'edit_category' => 'Edit Category',
    'delete_category' => 'Delete Category',
    'manage_categories' => 'Manage Categories',
    'no_category' => 'No Category',
    'select_category' => 'Select Category',
    'total_categories' => 'Total Categories',
    'with_images' => 'With Images',
    'empty_categories' => 'Empty Categories',
    'popular_categories' => 'Popular Categories',

    // Tags
    'all_tags' => 'All Tags',
    'tag_name' => 'Tag Name',
    'usage_count' => 'Usage Count',
    'add_tag' => 'Add Tag',
    'edit_tag' => 'Edit Tag',
    'delete_tag' => 'Delete Tag',
    'popular_tags' => 'Popular Tags',
    'manage_tags' => 'Manage Tags',
    'total_tags' => 'Total Tags',
    'used_tags' => 'Used Tags',
    'unused_tags' => 'Unused Tags',
    'separate_tags_with_commas' => 'Separate tags with commas',
    'tags_help_text' => 'Enter tags separated by commas',

    // Upload
    'upload_images' => 'Upload Images',
    'select_files' => 'Select Files',
    'drag_drop' => 'Drag files here or click to select',
    'drag_drop_images' => 'Drag images here',
    'or_click_to_select' => 'or click to select',
    'supported_formats' => 'Supported Formats',
    'max_file_size' => 'Max File Size',
    'upload_progress' => 'Upload Progress',
    'upload_complete' => 'Upload Complete',
    'upload_failed' => 'Upload Failed',
    'start_upload' => 'Start Upload',
    'uploading' => 'Uploading',
    'upload_description' => 'Upload new images to the gallery',
    'image_details' => 'Image Details',
    'select_images' => 'Select Images',
    'upload_tips' => 'Upload Tips',
    'tip_file_formats' => 'Supported formats: JPG, PNG, GIF, WebP',
    'tip_file_size' => 'Maximum size: 10MB',
    'tip_multiple_upload' => 'Multiple images can be uploaded at once',
    'tip_auto_thumbnails' => 'Thumbnails are generated automatically',
    'please_select_files' => 'Please select files first',
    'upload_completed' => 'Upload Completed',
    'images_uploaded' => 'images uploaded',
    'remove' => 'Remove',
    'upload_cancelled' => 'Upload Cancelled',
    'invalid_file_type' => 'Invalid file type',
    'file_too_big' => 'File too large',
    'upload_error' => 'Upload Error',
    'invalid_response' => 'Invalid Response',

    // Search and Filter
    'search_placeholder' => 'Search images...',
    'search_images' => 'Search Images',
    'filter_by_category' => 'Filter by Category',
    'filter_by_tag' => 'Filter by Tag',
    'sort_by' => 'Sort by',
    'sort_newest' => 'Newest',
    'sort_oldest' => 'Oldest',
    'sort_most_viewed' => 'Most Viewed',
    'sort_title' => 'Title',
    'clear_filters' => 'Clear Filters',
    'active_filters' => 'Active Filters',
    'filter' => 'Filter',
    'newest_first' => 'Newest First',
    'oldest_first' => 'Oldest First',
    'most_viewed' => 'Most Viewed',

    // Messages
    'confirm_delete' => 'Are you sure you want to delete?',
    'action_cannot_undone' => 'This action cannot be undone',
    'yes_delete' => 'Yes, Delete',
    'no_cancel' => 'No, Cancel',
    'delete_confirmation' => 'Delete Confirmation',
    'bulk_delete_confirmation' => 'Are you sure you want to delete selected items?',
    'bulk_delete_warning' => 'All selected items will be permanently deleted',
    'yes_delete_all' => 'Yes, Delete All',
    'confirm_bulk_delete' => 'Confirm Bulk Delete',

    // EXIF Info
    'camera_info' => 'Camera Info',
    'camera_make' => 'Camera Make',
    'camera_model' => 'Camera Model',
    'lens_model' => 'Lens Model',
    'focal_length' => 'Focal Length',
    'aperture' => 'Aperture',
    'shutter_speed' => 'Shutter Speed',
    'iso' => 'ISO',
    'flash' => 'Flash',
    'date_taken' => 'Date Taken',
    'gps_location' => 'GPS Location',
    'orientation' => 'Orientation',
    'color_space' => 'Color Space',
    'white_balance' => 'White Balance',
    'exposure_mode' => 'Exposure Mode',
    'metering_mode' => 'Metering Mode',

    // Statistics
    'statistics' => 'Statistics',
    'dashboard' => 'Dashboard',
    'total_images' => 'Total Images',
    'total_views' => 'Total Views',
    'recent_uploads' => 'Recent Uploads',
    'popular_images' => 'Popular Images',
    'storage_used' => 'Storage Used',
    'this_week' => 'This Week',
    'all_time' => 'All Time',
    'active' => 'Active',

    // Admin
    'admin_panel' => 'Admin Panel',
    'admin_login' => 'Admin Login',
    'admin_dashboard' => 'Admin Dashboard',
    'username_or_email' => 'Username or Email',
    'username' => 'Username',
    'email' => 'Email',
    'password' => 'Password',
    'remember_me' => 'Remember Me',
    'enter_username_or_email' => 'Enter username or email',
    'enter_password' => 'Enter password',
    'show_password' => 'Show Password',
    'hide_password' => 'Hide Password',
    'logging_in' => 'Logging In',
    'redirecting' => 'Redirecting',
    'back_to_site' => 'Back to Site',
    'secure_admin_area' => 'Secure Admin Area',
    'demo_login_info' => 'Demo Login Info',
    'use_demo_login' => 'Use Demo Login',
    'welcome_back' => 'Welcome Back',
    'view_site' => 'View Site',
    'profile' => 'Profile',
    'settings' => 'Settings',
    'change_password' => 'Change Password',
    'manage_images' => 'Manage Images',
    'manage_categories' => 'Manage Categories',
    'manage_tags' => 'Manage Tags',
    'tools' => 'Tools',
    'backup' => 'Backup',
    'cleanup' => 'Cleanup',
    'full_name' => 'Full Name',

    // Errors and Messages
    'please_fill_all_fields' => 'Please fill all required fields',
    'name_required_both_languages' => 'Name is required in both Arabic and English',
    'category_already_exists' => 'Category already exists',
    'category_added_successfully' => 'Category added successfully',
    'failed_to_add_category' => 'Failed to add category',
    'category_updated_successfully' => 'Category updated successfully',
    'failed_to_update_category' => 'Failed to update category',
    'category_deleted_successfully' => 'Category deleted successfully',
    'failed_to_delete_category' => 'Failed to delete category',
    'image_deleted_successfully' => 'Image deleted successfully',
    'failed_to_delete_image' => 'Failed to delete image',
    'images_deleted_successfully' => 'Images deleted successfully',
    'failed_to_delete_images' => 'Failed to delete images',
    'images_uploaded_successfully' => 'Images uploaded successfully',

    // Forms
    'name' => 'Name',
    'sort_order' => 'Sort Order',
    'is_active' => 'Active',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'actions' => 'Actions',
    'view' => 'View',
    'size' => 'Size',
    'created' => 'Created',

    // Pagination
    'first' => 'First',
    'last' => 'Last',
    'showing' => 'Showing',
    'to' => 'to',
    'of' => 'of',
    'results' => 'results',
    'page' => 'Page',

    // Delete and Confirmation
    'delete_image_confirmation' => 'Are you sure you want to delete this image?',
    'delete_category_confirmation' => 'Are you sure you want to delete this category?',
    'category_delete_warning' => 'If the category contains images, they will become uncategorized',
    'delete_selected' => 'Delete Selected',
    'images_selected' => 'images selected',
    'bulk_delete' => 'Bulk Delete',

    // Pages
    'no_images_yet' => 'No images yet',
    'start_by_uploading' => 'Start by uploading new images',
    'upload_now' => 'Upload Now',
    'no_images_found' => 'No images found',
    'no_images_description' => 'No images match the search criteria',
    'upload_first_image' => 'Upload First Image',
    'no_categories_yet' => 'No categories yet',
    'create_first_category' => 'Create the first category to organize images',
    'add_first_category' => 'Add First Category',
    'add_new_category' => 'Add New Category',
    'update_category' => 'Update Category',
    'no_tags_yet' => 'No tags yet',
    'add_first_tag' => 'Add First Tag',

    // Miscellaneous
    'all_rights_reserved' => 'All rights reserved',
    'link_copied' => 'Link copied to clipboard',
    'share_error' => 'Failed to share',
    'try_different_search' => 'Try different search terms',
    'back_to_gallery' => 'Back to Gallery',
    'related_images' => 'Related Images',
    'images_list' => 'Images List',
    'copy' => 'Copy',
    'saved' => 'Saved',
    'save_failed' => 'Save Failed',
    'auto_save' => 'Auto Save'
];
