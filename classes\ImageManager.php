<?php
/**
 * فئة إدارة الصور
 * Image Management Class
 */

class ImageManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع الصور مع التصفية والترقيم
     */
    public function getImages($filters = [], $page = 1, $perPage = null) {
        $perPage = $perPage ?: IMAGES_PER_PAGE;
        $offset = ($page - 1) * $perPage;
        
        $where = ['i.is_active = 1'];
        $params = [];
        
        // تصفية حسب الصنف
        if (!empty($filters['category_id'])) {
            $where[] = 'i.category_id = ?';
            $params[] = $filters['category_id'];
        }
        
        // تصفية حسب البحث
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $where[] = '(i.title LIKE ? OR i.title_ar LIKE ? OR i.description LIKE ? OR i.description_ar LIKE ?)';
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }
        
        // تصفية حسب العلامة
        if (!empty($filters['tag_id'])) {
            $where[] = 'EXISTS (SELECT 1 FROM image_tags it WHERE it.image_id = i.id AND it.tag_id = ?)';
            $params[] = $filters['tag_id'];
        }
        
        // ترتيب النتائج
        $orderBy = 'i.sort_order ASC, i.upload_date DESC';
        if (!empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'newest':
                    $orderBy = 'i.upload_date DESC';
                    break;
                case 'oldest':
                    $orderBy = 'i.upload_date ASC';
                    break;
                case 'most_viewed':
                    $orderBy = 'i.views_count DESC';
                    break;
                case 'title':
                    $orderBy = 'i.title ASC';
                    break;
            }
        }
        
        $whereClause = implode(' AND ', $where);
        
        $query = "SELECT i.*, c.name as category_name, c.name_ar as category_name_ar, c.slug as category_slug
                  FROM images i 
                  LEFT JOIN categories c ON i.category_id = c.id 
                  WHERE {$whereClause} 
                  ORDER BY {$orderBy} 
                  LIMIT {$perPage} OFFSET {$offset}";
        
        return $this->db->select($query, $params);
    }
    
    /**
     * عد الصور مع التصفية
     */
    public function countImages($filters = []) {
        $where = ['i.is_active = 1'];
        $params = [];
        
        if (!empty($filters['category_id'])) {
            $where[] = 'i.category_id = ?';
            $params[] = $filters['category_id'];
        }
        
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $where[] = '(i.title LIKE ? OR i.title_ar LIKE ? OR i.description LIKE ? OR i.description_ar LIKE ?)';
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }
        
        if (!empty($filters['tag_id'])) {
            $where[] = 'EXISTS (SELECT 1 FROM image_tags it WHERE it.image_id = i.id AND it.tag_id = ?)';
            $params[] = $filters['tag_id'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        $query = "SELECT COUNT(*) as count FROM images i WHERE {$whereClause}";
        $result = $this->db->selectOne($query, $params);
        
        return $result ? (int)$result['count'] : 0;
    }
    
    /**
     * الحصول على صورة واحدة
     */
    public function getImage($id) {
        $query = "SELECT i.*, c.name as category_name, c.name_ar as category_name_ar, c.slug as category_slug
                  FROM images i 
                  LEFT JOIN categories c ON i.category_id = c.id 
                  WHERE i.id = ? AND i.is_active = 1";
        
        return $this->db->selectOne($query, [$id]);
    }
    
    /**
     * الحصول على صورة بالاسم
     */
    public function getImageByFilename($filename) {
        $query = "SELECT i.*, c.name as category_name, c.name_ar as category_name_ar, c.slug as category_slug
                  FROM images i 
                  LEFT JOIN categories c ON i.category_id = c.id 
                  WHERE i.filename = ? AND i.is_active = 1";
        
        return $this->db->selectOne($query, [$filename]);
    }
    
    /**
     * إضافة صورة جديدة
     */
    public function addImage($data) {
        $query = "INSERT INTO images (filename, original_filename, title, title_ar, description, description_ar, 
                  alt_text, alt_text_ar, file_size, width, height, mime_type, file_path, thumbnail_path, 
                  medium_path, category_id, sort_order) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['filename'],
            $data['original_filename'],
            $data['title'] ?? '',
            $data['title_ar'] ?? '',
            $data['description'] ?? '',
            $data['description_ar'] ?? '',
            $data['alt_text'] ?? '',
            $data['alt_text_ar'] ?? '',
            $data['file_size'],
            $data['width'],
            $data['height'],
            $data['mime_type'],
            $data['file_path'],
            $data['thumbnail_path'] ?? '',
            $data['medium_path'] ?? '',
            $data['category_id'] ?? null,
            $data['sort_order'] ?? 0
        ];
        
        return $this->db->insert($query, $params);
    }
    
    /**
     * تحديث صورة
     */
    public function updateImage($id, $data) {
        $fields = [];
        $params = [];
        
        $allowedFields = ['title', 'title_ar', 'description', 'description_ar', 'alt_text', 
                         'alt_text_ar', 'category_id', 'sort_order', 'is_active'];
        
        foreach ($allowedFields as $field) {
            if (array_key_exists($field, $data)) {
                $fields[] = "{$field} = ?";
                $params[] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            return false;
        }
        
        $params[] = $id;
        $query = "UPDATE images SET " . implode(', ', $fields) . ", updated_at = NOW() WHERE id = ?";
        
        return $this->db->update($query, $params);
    }
    
    /**
     * حذف صورة
     */
    public function deleteImage($id) {
        // الحصول على معلومات الصورة أولاً
        $image = $this->getImage($id);
        if (!$image) {
            return false;
        }
        
        // حذف الملفات الفعلية
        $this->deleteImageFiles($image);
        
        // حذف السجل من قاعدة البيانات
        $query = "DELETE FROM images WHERE id = ?";
        return $this->db->delete($query, [$id]);
    }
    
    /**
     * حذف ملفات الصورة من الخادم
     */
    private function deleteImageFiles($image) {
        $files = [
            ROOT_PATH . '/' . $image['file_path'],
            ROOT_PATH . '/' . $image['thumbnail_path'],
            ROOT_PATH . '/' . $image['medium_path']
        ];
        
        foreach ($files as $file) {
            if ($file && file_exists($file)) {
                unlink($file);
            }
        }
    }
    
    /**
     * زيادة عدد المشاهدات
     */
    public function incrementViews($id) {
        $query = "UPDATE images SET views_count = views_count + 1 WHERE id = ?";
        return $this->db->update($query, [$id]);
    }
    
    /**
     * الحصول على الصور الأحدث
     */
    public function getRecentImages($limit = null) {
        $limit = $limit ?: RECENT_IMAGES_COUNT;
        
        $query = "SELECT i.*, c.name as category_name, c.name_ar as category_name_ar, c.slug as category_slug
                  FROM images i 
                  LEFT JOIN categories c ON i.category_id = c.id 
                  WHERE i.is_active = 1 
                  ORDER BY i.upload_date DESC 
                  LIMIT {$limit}";
        
        return $this->db->select($query);
    }
    
    /**
     * الحصول على الصور الأكثر مشاهدة
     */
    public function getPopularImages($limit = null) {
        $limit = $limit ?: RECENT_IMAGES_COUNT;
        
        $query = "SELECT i.*, c.name as category_name, c.name_ar as category_name_ar, c.slug as category_slug
                  FROM images i 
                  LEFT JOIN categories c ON i.category_id = c.id 
                  WHERE i.is_active = 1 AND i.views_count > 0
                  ORDER BY i.views_count DESC 
                  LIMIT {$limit}";
        
        return $this->db->select($query);
    }
    
    /**
     * الحصول على صور عشوائية
     */
    public function getRandomImages($limit = null) {
        $limit = $limit ?: RECENT_IMAGES_COUNT;
        
        $query = "SELECT i.*, c.name as category_name, c.name_ar as category_name_ar, c.slug as category_slug
                  FROM images i 
                  LEFT JOIN categories c ON i.category_id = c.id 
                  WHERE i.is_active = 1 
                  ORDER BY RAND() 
                  LIMIT {$limit}";
        
        return $this->db->select($query);
    }
    
    /**
     * الحصول على علامات الصورة
     */
    public function getImageTags($imageId) {
        $query = "SELECT t.* FROM tags t 
                  INNER JOIN image_tags it ON t.id = it.tag_id 
                  WHERE it.image_id = ? 
                  ORDER BY t.name";
        
        return $this->db->select($query, [$imageId]);
    }
    
    /**
     * إضافة علامات للصورة
     */
    public function addImageTags($imageId, $tagIds) {
        if (empty($tagIds)) {
            return true;
        }
        
        // حذف العلامات الحالية
        $this->removeImageTags($imageId);
        
        // إضافة العلامات الجديدة
        $query = "INSERT INTO image_tags (image_id, tag_id) VALUES (?, ?)";
        
        foreach ($tagIds as $tagId) {
            $this->db->insert($query, [$imageId, $tagId]);
        }
        
        return true;
    }
    
    /**
     * حذف علامات الصورة
     */
    public function removeImageTags($imageId) {
        $query = "DELETE FROM image_tags WHERE image_id = ?";
        return $this->db->delete($query, [$imageId]);
    }
}
?>
