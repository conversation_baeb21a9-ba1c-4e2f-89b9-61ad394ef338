<?php
/**
 * إعدادات النظام الأساسية
 * Photo Gallery Configuration File
 */

// منع الوصول المباشر
if (!defined('GALLERY_ACCESS')) {
    die('Direct access not allowed');
}

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'photo_gallery');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام العامة
define('SITE_NAME', 'معرض الصور');
define('SITE_NAME_EN', 'Photo Gallery');
define('SITE_DESCRIPTION', 'معرض صور احترافي مع لوحة إدارة متقدمة');
define('SITE_KEYWORDS', 'صور، معرض، تصوير، فوتوغرافيا');
define('SITE_URL', 'http://localhost/atvg');
define('ADMIN_EMAIL', '<EMAIL>');

// إعدادات اللغة والتوطين
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);
define('RTL_LANGUAGES', ['ar']);

// إعدادات الصور
define('UPLOAD_DIR', 'uploads/');
define('THUMBNAILS_DIR', 'uploads/thumbnails/');
define('MEDIUM_DIR', 'uploads/medium/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_MIME_TYPES', [
    'image/jpeg',
    'image/png', 
    'image/gif',
    'image/webp'
]);

// أحجام الصور
define('THUMBNAIL_WIDTH', 300);
define('THUMBNAIL_HEIGHT', 300);
define('MEDIUM_WIDTH', 800);
define('MEDIUM_HEIGHT', 600);
define('MAX_WIDTH', 1920);
define('MAX_HEIGHT', 1080);

// إعدادات العرض
define('IMAGES_PER_PAGE', 12);
define('ADMIN_IMAGES_PER_PAGE', 20);
define('RECENT_IMAGES_COUNT', 6);

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات التخزين المؤقت
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 3600); // ساعة واحدة

// إعدادات التطوير
define('DEBUG_MODE', true);
define('SHOW_ERRORS', true);
define('LOG_ERRORS', true);
define('ERROR_LOG_FILE', 'logs/error.log');

// إعدادات SEO
define('META_TITLE_SEPARATOR', ' | ');
define('DEFAULT_META_DESCRIPTION', 'معرض صور احترافي يعرض أجمل الصور المصنفة حسب الفئات');
define('DEFAULT_META_KEYWORDS', 'صور, معرض, تصوير, فوتوغرافيا, طبيعة, بورتريه');

// إعدادات الشبكات الاجتماعية
define('FACEBOOK_URL', '');
define('TWITTER_URL', '');
define('INSTAGRAM_URL', '');
define('YOUTUBE_URL', '');

// إعدادات البريد الإلكتروني (للإشعارات)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// مسارات النظام
define('ROOT_PATH', __DIR__);
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('CLASSES_PATH', ROOT_PATH . '/classes');
define('ADMIN_PATH', ROOT_PATH . '/admin');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('LOGS_PATH', ROOT_PATH . '/logs');

// URLs
define('BASE_URL', SITE_URL);
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/' . UPLOAD_DIR);
define('THUMBNAILS_URL', BASE_URL . '/' . THUMBNAILS_DIR);
define('MEDIUM_URL', BASE_URL . '/' . MEDIUM_DIR);
define('ADMIN_URL', BASE_URL . '/admin');

// إعدادات الجلسة
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // تغيير إلى 1 في HTTPS

// إعدادات الأخطاء
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// إعدادات المنطقة الزمنية
date_default_timezone_set('Asia/Riyadh');

// إعدادات إضافية
define('WATERMARK_ENABLED', false);
define('WATERMARK_IMAGE', 'assets/images/watermark.png');
define('WATERMARK_POSITION', 'bottom-right');
define('WATERMARK_OPACITY', 50);

// إعدادات النسخ الاحتياطي
define('BACKUP_ENABLED', false);
define('BACKUP_DIR', 'backups/');
define('AUTO_BACKUP_INTERVAL', 86400); // يومياً

// رسائل النظام
define('SUCCESS_MESSAGES', [
    'ar' => [
        'upload_success' => 'تم رفع الصورة بنجاح',
        'delete_success' => 'تم حذف الصورة بنجاح',
        'update_success' => 'تم تحديث البيانات بنجاح',
        'category_created' => 'تم إنشاء الصنف بنجاح',
        'login_success' => 'تم تسجيل الدخول بنجاح'
    ],
    'en' => [
        'upload_success' => 'Image uploaded successfully',
        'delete_success' => 'Image deleted successfully', 
        'update_success' => 'Data updated successfully',
        'category_created' => 'Category created successfully',
        'login_success' => 'Login successful'
    ]
]);

define('ERROR_MESSAGES', [
    'ar' => [
        'upload_failed' => 'فشل في رفع الصورة',
        'invalid_file' => 'نوع الملف غير مدعوم',
        'file_too_large' => 'حجم الملف كبير جداً',
        'database_error' => 'خطأ في قاعدة البيانات',
        'access_denied' => 'غير مسموح بالوصول',
        'login_failed' => 'فشل في تسجيل الدخول'
    ],
    'en' => [
        'upload_failed' => 'Failed to upload image',
        'invalid_file' => 'File type not supported',
        'file_too_large' => 'File size too large',
        'database_error' => 'Database error',
        'access_denied' => 'Access denied',
        'login_failed' => 'Login failed'
    ]
]);

// التحقق من وجود المجلدات المطلوبة
$required_dirs = [
    ROOT_PATH . '/' . UPLOAD_DIR,
    ROOT_PATH . '/' . THUMBNAILS_DIR,
    ROOT_PATH . '/' . MEDIUM_DIR,
    LOGS_PATH
];

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// إنشاء ملف .htaccess لحماية مجلد uploads
$htaccess_content = "Options -Indexes\n";
$htaccess_content .= "<Files ~ \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">\n";
$htaccess_content .= "    deny from all\n";
$htaccess_content .= "</Files>\n";

$htaccess_file = ROOT_PATH . '/' . UPLOAD_DIR . '.htaccess';
if (!file_exists($htaccess_file)) {
    file_put_contents($htaccess_file, $htaccess_content);
}
?>
