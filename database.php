<?php
/**
 * فئة الاتصال بقاعدة البيانات
 * Database Connection Class
 */

// منع الوصول المباشر
if (!defined('GALLERY_ACCESS')) {
    die('Direct access not allowed');
}

class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;

    /**
     * منشئ الفئة
     */
    private function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;
        
        $this->connect();
    }

    /**
     * الحصول على مثيل واحد من الفئة (Singleton Pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset} COLLATE {$this->charset}_unicode_ci"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch (PDOException $e) {
            $this->logError("Database connection failed: " . $e->getMessage());
            die("خطأ في الاتصال بقاعدة البيانات");
        }
    }

    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection() {
        return $this->connection;
    }

    /**
     * تنفيذ استعلام SELECT
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            $this->logError("Select query failed: " . $e->getMessage() . " Query: " . $query);
            return false;
        }
    }

    /**
     * تنفيذ استعلام SELECT لصف واحد
     */
    public function selectOne($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            $this->logError("SelectOne query failed: " . $e->getMessage() . " Query: " . $query);
            return false;
        }
    }

    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $result = $stmt->execute($params);
            return $result ? $this->connection->lastInsertId() : false;
        } catch (PDOException $e) {
            $this->logError("Insert query failed: " . $e->getMessage() . " Query: " . $query);
            return false;
        }
    }

    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->logError("Update query failed: " . $e->getMessage() . " Query: " . $query);
            return false;
        }
    }

    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->logError("Delete query failed: " . $e->getMessage() . " Query: " . $query);
            return false;
        }
    }

    /**
     * تنفيذ استعلام عام
     */
    public function execute($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            $this->logError("Execute query failed: " . $e->getMessage() . " Query: " . $query);
            return false;
        }
    }

    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }

    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }

    /**
     * الحصول على عدد الصفوف
     */
    public function count($table, $where = '', $params = []) {
        $query = "SELECT COUNT(*) as count FROM {$table}";
        if (!empty($where)) {
            $query .= " WHERE {$where}";
        }
        
        $result = $this->selectOne($query, $params);
        return $result ? (int)$result['count'] : 0;
    }

    /**
     * التحقق من وجود سجل
     */
    public function exists($table, $where, $params = []) {
        $query = "SELECT 1 FROM {$table} WHERE {$where} LIMIT 1";
        $result = $this->selectOne($query, $params);
        return $result !== false;
    }

    /**
     * الحصول على آخر معرف مدرج
     */
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }

    /**
     * تسجيل الأخطاء
     */
    private function logError($message) {
        if (LOG_ERRORS && defined('ERROR_LOG_FILE')) {
            $timestamp = date('Y-m-d H:i:s');
            $logMessage = "[{$timestamp}] DATABASE ERROR: {$message}" . PHP_EOL;
            file_put_contents(ERROR_LOG_FILE, $logMessage, FILE_APPEND | LOCK_EX);
        }
    }

    /**
     * تنظيف البيانات
     */
    public function sanitize($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitize'], $data);
        }
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }

    /**
     * إنشاء slug من النص
     */
    public function createSlug($text) {
        // تحويل النص العربي والإنجليزي إلى slug
        $text = trim($text);
        $text = mb_strtolower($text, 'UTF-8');
        
        // استبدال المسافات والرموز الخاصة
        $text = preg_replace('/[^\p{L}\p{N}\s\-_]/u', '', $text);
        $text = preg_replace('/[\s\-_]+/', '-', $text);
        $text = trim($text, '-');
        
        return $text;
    }

    /**
     * إنشاء slug فريد
     */
    public function createUniqueSlug($table, $column, $text, $id = null) {
        $baseSlug = $this->createSlug($text);
        $slug = $baseSlug;
        $counter = 1;
        
        while (true) {
            $query = "SELECT id FROM {$table} WHERE {$column} = ?";
            $params = [$slug];
            
            if ($id) {
                $query .= " AND id != ?";
                $params[] = $id;
            }
            
            if (!$this->selectOne($query, $params)) {
                break;
            }
            
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * منع استنساخ الكائن
     */
    private function __clone() {}

    /**
     * منع إلغاء تسلسل الكائن
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * دالة مساعدة للحصول على قاعدة البيانات
 */
function getDB() {
    return Database::getInstance();
}

/**
 * دالة مساعدة لتنظيف البيانات
 */
function sanitize($data) {
    return Database::getInstance()->sanitize($data);
}

/**
 * دالة مساعدة لإنشاء slug
 */
function createSlug($text) {
    return Database::getInstance()->createSlug($text);
}
?>
