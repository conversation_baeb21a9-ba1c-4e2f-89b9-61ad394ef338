/**
 * ملف JavaScript الرئيسي لمعرض الصور
 * Main JavaScript file for Photo Gallery
 */

(function() {
    'use strict';

    // متغيرات عامة
    let currentLanguage = document.documentElement.lang || 'ar';
    let isRTL = document.documentElement.dir === 'rtl';
    
    // النصوص متعددة اللغات
    const translations = {
        ar: {
            loading: 'جاري التحميل...',
            error: 'حدث خطأ',
            success: 'تم بنجاح',
            confirm: 'تأكيد',
            cancel: 'إلغاء',
            close: 'إغلاق',
            search: 'بحث',
            noResults: 'لا توجد نتائج',
            linkCopied: 'تم نسخ الرابط',
            shareError: 'فشل في المشاركة',
            imageLoaded: 'تم تحميل الصورة',
            imageError: 'فشل في تحميل الصورة'
        },
        en: {
            loading: 'Loading...',
            error: 'An error occurred',
            success: 'Success',
            confirm: 'Confirm',
            cancel: 'Cancel',
            close: 'Close',
            search: 'Search',
            noResults: 'No results found',
            linkCopied: 'Link copied to clipboard',
            shareError: 'Failed to share',
            imageLoaded: 'Image loaded',
            imageError: 'Failed to load image'
        }
    };

    // دالة للحصول على النص المترجم
    function __(key) {
        return translations[currentLanguage] && translations[currentLanguage][key] 
            ? translations[currentLanguage][key] 
            : key;
    }

    // تهيئة التطبيق عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        initializeGallery();
        initializeLazyLoading();
        initializeSearch();
        initializeFilters();
        initializeImageActions();
        initializeKeyboardNavigation();
        initializeTooltips();
        initializeAnimations();
    });

    // تهيئة المعرض
    function initializeGallery() {
        // تحسين عرض الصور
        const galleryItems = document.querySelectorAll('.gallery-item');
        
        galleryItems.forEach(function(item, index) {
            // إضافة تأثير التحميل المتدرج
            item.style.animationDelay = (index * 0.1) + 's';
            item.classList.add('fade-in');
            
            // إضافة معالج النقر
            const image = item.querySelector('.gallery-thumbnail');
            if (image) {
                image.addEventListener('load', function() {
                    item.classList.add('loaded');
                });
                
                image.addEventListener('error', function() {
                    item.classList.add('error');
                    console.warn('Failed to load image:', image.src);
                });
            }
        });

        // تحسين Lightbox
        if (typeof lightbox !== 'undefined') {
            lightbox.option({
                'resizeDuration': 200,
                'wrapAround': true,
                'albumLabel': currentLanguage === 'ar' ? 'صورة %1 من %2' : 'Image %1 of %2',
                'fadeDuration': 300,
                'imageFadeDuration': 300
            });
        }
    }

    // تهيئة التحميل الكسول للصور
    function initializeLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src || img.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            const lazyImages = document.querySelectorAll('img.lazy');
            lazyImages.forEach(function(img) {
                imageObserver.observe(img);
            });
        }
    }

    // تهيئة البحث
    function initializeSearch() {
        const searchForm = document.querySelector('form[action="index.php"]');
        const searchInput = document.querySelector('input[name="search"]');
        
        if (searchForm && searchInput) {
            // البحث التلقائي أثناء الكتابة (مع تأخير)
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    if (searchInput.value.length >= 3 || searchInput.value.length === 0) {
                        // يمكن إضافة البحث AJAX هنا
                        console.log('Search for:', searchInput.value);
                    }
                }, 500);
            });

            // تحسين تجربة البحث
            searchInput.addEventListener('focus', function() {
                this.parentElement.classList.add('search-focused');
            });

            searchInput.addEventListener('blur', function() {
                this.parentElement.classList.remove('search-focused');
            });
        }
    }

    // تهيئة المرشحات
    function initializeFilters() {
        const filterLinks = document.querySelectorAll('.list-group-item, .dropdown-item');
        
        filterLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                // إضافة تأثير التحميل
                if (this.href && this.href !== '#') {
                    const spinner = document.createElement('span');
                    spinner.className = 'loading-spinner ms-2';
                    this.appendChild(spinner);
                }
            });
        });

        // تحديث عدادات الفئات (إذا كان هناك AJAX)
        updateCategoryCounts();
    }

    // تحديث عدادات الفئات
    function updateCategoryCounts() {
        const categoryLinks = document.querySelectorAll('.list-group-item[href*="category="]');
        
        categoryLinks.forEach(function(link) {
            const badge = link.querySelector('.badge');
            if (badge) {
                // يمكن إضافة AJAX لتحديث العدادات هنا
            }
        });
    }

    // تهيئة إجراءات الصور
    function initializeImageActions() {
        // أزرار المشاركة
        const shareButtons = document.querySelectorAll('[onclick*="shareImage"]');
        shareButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                shareImage();
            });
        });

        // أزرار التحميل
        const downloadButtons = document.querySelectorAll('a[download]');
        downloadButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                // تتبع التحميلات
                console.log('Image downloaded:', this.href);
            });
        });

        // تحسين عرض الصورة الرئيسية
        const mainImage = document.getElementById('main-image');
        if (mainImage) {
            mainImage.addEventListener('load', function() {
                this.classList.add('loaded');
            });

            mainImage.addEventListener('error', function() {
                this.classList.add('error');
                console.error('Failed to load main image');
            });
        }
    }

    // مشاركة الصورة
    function shareImage() {
        const title = document.title;
        const url = window.location.href;
        const text = document.querySelector('meta[name="description"]')?.content || title;

        if (navigator.share) {
            navigator.share({
                title: title,
                text: text,
                url: url
            }).then(function() {
                console.log('Shared successfully');
            }).catch(function(error) {
                console.error('Error sharing:', error);
                fallbackShare(url);
            });
        } else {
            fallbackShare(url);
        }
    }

    // مشاركة احتياطية
    function fallbackShare(url) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(function() {
                showNotification(__('linkCopied'), 'success');
            }).catch(function() {
                showShareModal(url);
            });
        } else {
            showShareModal(url);
        }
    }

    // عرض نافذة المشاركة
    function showShareModal(url) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${__('share')}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="input-group">
                            <input type="text" class="form-control" value="${url}" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('${url}')">
                                ${__('copy')}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
    }

    // نسخ إلى الحافظة
    window.copyToClipboard = function(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification(__('linkCopied'), 'success');
    };

    // تهيئة التنقل بلوحة المفاتيح
    function initializeKeyboardNavigation() {
        document.addEventListener('keydown', function(e) {
            // ESC لإغلاق النوافذ المنبثقة
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal.show');
                modals.forEach(function(modal) {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) bsModal.hide();
                });
            }

            // أسهم التنقل في المعرض
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                const currentImage = document.querySelector('.gallery-item.active');
                if (currentImage) {
                    const direction = (e.key === 'ArrowLeft') ? (isRTL ? 1 : -1) : (isRTL ? -1 : 1);
                    navigateGallery(direction);
                }
            }
        });
    }

    // التنقل في المعرض
    function navigateGallery(direction) {
        const galleryItems = Array.from(document.querySelectorAll('.gallery-item'));
        const currentIndex = galleryItems.findIndex(item => item.classList.contains('active'));
        
        if (currentIndex !== -1) {
            const nextIndex = currentIndex + direction;
            if (nextIndex >= 0 && nextIndex < galleryItems.length) {
                galleryItems[currentIndex].classList.remove('active');
                galleryItems[nextIndex].classList.add('active');
                galleryItems[nextIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    }

    // تهيئة التلميحات
    function initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // تهيئة الرسوم المتحركة
    function initializeAnimations() {
        // تحريك العناصر عند الظهور
        if ('IntersectionObserver' in window) {
            const animationObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, { threshold: 0.1 });

            const animatedElements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right');
            animatedElements.forEach(function(el) {
                animationObserver.observe(el);
            });
        }
    }

    // عرض الإشعارات
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // إزالة الإشعار تلقائياً بعد 5 ثوان
        setTimeout(function() {
            if (notification.parentNode) {
                notification.classList.remove('show');
                setTimeout(function() {
                    if (notification.parentNode) {
                        document.body.removeChild(notification);
                    }
                }, 150);
            }
        }, 5000);
    }

    // تحسين الأداء
    function optimizePerformance() {
        // تأجيل تحميل الصور غير المرئية
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(function(img) {
            img.loading = 'lazy';
        });

        // ضغط الصور للشاشات الصغيرة
        if (window.innerWidth < 768) {
            const thumbnails = document.querySelectorAll('.gallery-thumbnail');
            thumbnails.forEach(function(img) {
                if (img.src.includes('medium_')) {
                    img.src = img.src.replace('medium_', 'thumb_');
                }
            });
        }
    }

    // تهيئة تحسينات الأداء
    optimizePerformance();

    // إعادة تهيئة عند تغيير حجم النافذة
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            optimizePerformance();
        }, 250);
    });

    // تصدير الدوال للاستخدام العام
    window.GalleryApp = {
        shareImage: shareImage,
        showNotification: showNotification,
        navigateGallery: navigateGallery,
        __: __
    };

})();
