/**
 * أنماط لوحة الإدارة
 * Admin Panel Styles
 */

/* ===== متغيرات الألوان ===== */
:root {
    --admin-primary: #007bff;
    --admin-secondary: #6c757d;
    --admin-success: #28a745;
    --admin-danger: #dc3545;
    --admin-warning: #ffc107;
    --admin-info: #17a2b8;
    --admin-light: #f8f9fa;
    --admin-dark: #343a40;
    --admin-sidebar-bg: #2c3e50;
    --admin-sidebar-hover: #34495e;
    --admin-border: #dee2e6;
}

/* ===== الجسم الرئيسي ===== */
.admin-body {
    background-color: #f4f6f9;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ===== شريط التنقل ===== */
.navbar-dark {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.4rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* ===== البطاقات الإحصائية ===== */
.stat-card {
    border: none;
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stat-card .card-body {
    padding: 1.5rem;
}

.stat-card h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0;
}

.stat-card .stat-icon {
    opacity: 0.3;
}

.stat-card .card-title {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

/* ===== بطاقات الصور الأخيرة ===== */
.recent-image-card {
    border: none;
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.recent-image-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}

.recent-image-card .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
    opacity: 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding: 1rem;
}

.recent-image-card:hover .image-overlay {
    opacity: 1;
}

.recent-image-card .image-actions {
    display: flex;
    gap: 0.5rem;
}

.recent-image-card .image-actions .btn {
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* ===== منطقة الرفع (Dropzone) ===== */
.dropzone {
    border: 2px dashed #007bff;
    border-radius: 1rem;
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.dropzone:hover,
.dropzone.dz-drag-hover {
    border-color: #0056b3;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    transform: scale(1.02);
}

.dropzone .dz-message {
    margin: 0;
}

.dropzone .dz-message .dz-icon {
    margin-bottom: 1rem;
}

.dropzone .dz-message h4 {
    color: #007bff;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.dropzone .dz-preview {
    display: inline-block;
    margin: 0.5rem;
    border-radius: 0.5rem;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.dropzone .dz-preview .dz-image {
    border-radius: 0.5rem 0.5rem 0 0;
}

.dropzone .dz-preview .dz-details {
    padding: 0.5rem;
    background: white;
}

.dropzone .dz-preview .dz-progress {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.dropzone .dz-preview .dz-upload {
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    height: 100%;
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* ===== النماذج ===== */
.form-control,
.form-select {
    border-radius: 0.5rem;
    border: 1px solid #e3e6f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

/* ===== الأزرار ===== */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

/* ===== الجداول ===== */
.table {
    background: white;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    border-color: #f3f3f3;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8f9ff;
}

/* ===== شريط التقدم ===== */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
    border-radius: 4px;
}

/* ===== التنبيهات ===== */
.alert {
    border: none;
    border-radius: 0.75rem;
    padding: 1rem 1.5rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* ===== النوافذ المنبثقة ===== */
.modal-content {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 1rem 1rem 0 0;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 1rem 1rem;
    padding: 1rem 1.5rem;
}

/* ===== العلامات ===== */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

/* ===== التحميل ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== الاستجابة للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .stat-card h2 {
        font-size: 2rem;
    }
    
    .stat-card .card-body {
        padding: 1rem;
    }
    
    .dropzone {
        padding: 2rem 1rem;
    }
    
    .recent-image-card .image-overlay {
        opacity: 1;
        background: linear-gradient(to bottom, transparent 60%, rgba(0,0,0,0.8) 100%);
    }
    
    .navbar-nav .nav-link {
        padding: 0.5rem 0.75rem;
        margin: 0.125rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .stat-card h2 {
        font-size: 1.75rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .table thead th,
    .table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* ===== تحسينات إضافية ===== */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}

.text-gradient {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, #007bff 0%, #0056b3 100%) 1;
}

/* ===== تأثيرات الحركة ===== */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== تخصيصات خاصة ===== */
.admin-sidebar {
    background: var(--admin-sidebar-bg);
    min-height: 100vh;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
}

.admin-content {
    background: #f4f6f9;
    min-height: 100vh;
    padding: 2rem;
}

.quick-actions {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.quick-actions .btn {
    border-radius: 50%;
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .navbar,
    .btn,
    .quick-actions,
    .image-overlay,
    .dropzone {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    body {
        background: white;
    }
}
