# ===================================
# Docker Compose لمعرض الصور
# Photo Gallery Docker Compose
# ===================================

version: '3.8'

services:
  # خادم الويب مع PHP
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: photo_gallery_web
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - ./uploads:/var/www/html/uploads
      - ./logs:/var/www/html/logs
    environment:
      - PHP_DISPLAY_ERRORS=1
      - PHP_ERROR_REPORTING=E_ALL
      - DB_HOST=mysql
      - DB_NAME=photo_gallery
      - DB_USER=gallery_user
      - DB_PASS=gallery_password
    depends_on:
      - mysql
      - redis
    networks:
      - gallery_network
    restart: unless-stopped

  # قاعدة بيانات MySQL
  mysql:
    image: mysql:8.0
    container_name: photo_gallery_mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: photo_gallery
      MYSQL_USER: gallery_user
      MYSQL_PASSWORD: gallery_password
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/sample-data.sql:/docker-entrypoint-initdb.d/02-sample-data.sql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - gallery_network
    restart: unless-stopped

  # Redis للتخزين المؤقت
  redis:
    image: redis:7-alpine
    container_name: photo_gallery_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - gallery_network
    restart: unless-stopped

  # phpMyAdmin لإدارة قاعدة البيانات
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: photo_gallery_phpmyadmin
    ports:
      - "8081:80"
    environment:
      PMA_HOST: mysql
      PMA_USER: gallery_user
      PMA_PASSWORD: gallery_password
      MYSQL_ROOT_PASSWORD: root_password
    depends_on:
      - mysql
    networks:
      - gallery_network
    restart: unless-stopped

  # Nginx (اختياري - للإنتاج)
  nginx:
    image: nginx:alpine
    container_name: photo_gallery_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - .:/var/www/html
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./docker/ssl:/etc/nginx/ssl
    depends_on:
      - web
    networks:
      - gallery_network
    restart: unless-stopped
    profiles:
      - production

  # Elasticsearch للبحث المتقدم (اختياري)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: photo_gallery_elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - gallery_network
    restart: unless-stopped
    profiles:
      - advanced

# الشبكات
networks:
  gallery_network:
    driver: bridge

# وحدات التخزين
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
