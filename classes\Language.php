<?php
/**
 * فئة إدارة اللغات والترجمة
 * Language Management Class
 */

class Language {
    private static $instance = null;
    private $currentLanguage;
    private $translations = [];
    private $loadedLanguages = [];
    
    private function __construct() {
        $this->currentLanguage = getCurrentLanguage();
        $this->loadLanguage($this->currentLanguage);
    }
    
    /**
     * الحصول على مثيل واحد من الفئة
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * تحميل ملف اللغة
     */
    private function loadLanguage($lang) {
        if (in_array($lang, $this->loadedLanguages)) {
            return;
        }
        
        $langFile = ROOT_PATH . "/languages/{$lang}.php";
        
        if (file_exists($langFile)) {
            $translations = include $langFile;
            if (is_array($translations)) {
                $this->translations[$lang] = $translations;
                $this->loadedLanguages[] = $lang;
            }
        } else {
            // إنشاء ملف اللغة إذا لم يكن موجوداً
            $this->createLanguageFile($lang);
        }
    }
    
    /**
     * إنشاء ملف لغة افتراضي
     */
    private function createLanguageFile($lang) {
        $langDir = ROOT_PATH . '/languages';
        if (!is_dir($langDir)) {
            mkdir($langDir, 0755, true);
        }
        
        $defaultTranslations = $this->getDefaultTranslations($lang);
        $langFile = $langDir . "/{$lang}.php";
        
        $content = "<?php\n";
        $content .= "/**\n";
        $content .= " * ملف ترجمة اللغة: {$lang}\n";
        $content .= " * Language file: {$lang}\n";
        $content .= " */\n\n";
        $content .= "return " . var_export($defaultTranslations, true) . ";\n";
        
        file_put_contents($langFile, $content);
        
        $this->translations[$lang] = $defaultTranslations;
        $this->loadedLanguages[] = $lang;
    }
    
    /**
     * الحصول على الترجمات الافتراضية
     */
    private function getDefaultTranslations($lang) {
        $translations = [
            'ar' => [
                // العام
                'home' => 'الرئيسية',
                'gallery' => 'المعرض',
                'categories' => 'الأصناف',
                'tags' => 'العلامات',
                'search' => 'بحث',
                'admin' => 'الإدارة',
                'login' => 'تسجيل الدخول',
                'logout' => 'تسجيل الخروج',
                'upload' => 'رفع',
                'edit' => 'تعديل',
                'delete' => 'حذف',
                'save' => 'حفظ',
                'cancel' => 'إلغاء',
                'back' => 'رجوع',
                'next' => 'التالي',
                'previous' => 'السابق',
                'loading' => 'جاري التحميل...',
                'no_results' => 'لا توجد نتائج',
                'view_all' => 'عرض الكل',
                'show_more' => 'عرض المزيد',
                
                // الصور
                'images' => 'الصور',
                'image' => 'صورة',
                'title' => 'العنوان',
                'description' => 'الوصف',
                'category' => 'الصنف',
                'upload_date' => 'تاريخ الرفع',
                'file_size' => 'حجم الملف',
                'dimensions' => 'الأبعاد',
                'views' => 'المشاهدات',
                'download' => 'تحميل',
                'share' => 'مشاركة',
                'fullscreen' => 'ملء الشاشة',
                'slideshow' => 'عرض الشرائح',
                
                // الأصناف
                'all_categories' => 'جميع الأصناف',
                'category_name' => 'اسم الصنف',
                'category_description' => 'وصف الصنف',
                'images_count' => 'عدد الصور',
                'add_category' => 'إضافة صنف',
                'edit_category' => 'تعديل الصنف',
                'delete_category' => 'حذف الصنف',
                
                // العلامات
                'all_tags' => 'جميع العلامات',
                'tag_name' => 'اسم العلامة',
                'usage_count' => 'عدد الاستخدام',
                'add_tag' => 'إضافة علامة',
                'edit_tag' => 'تعديل العلامة',
                'delete_tag' => 'حذف العلامة',
                'popular_tags' => 'العلامات الشائعة',
                
                // الرفع
                'upload_images' => 'رفع الصور',
                'select_files' => 'اختيار الملفات',
                'drag_drop' => 'اسحب الملفات هنا أو انقر للاختيار',
                'supported_formats' => 'الصيغ المدعومة',
                'max_file_size' => 'الحد الأقصى لحجم الملف',
                'upload_progress' => 'تقدم الرفع',
                'upload_complete' => 'اكتمل الرفع',
                'upload_failed' => 'فشل الرفع',
                
                // البحث والتصفية
                'search_placeholder' => 'ابحث في الصور...',
                'filter_by_category' => 'تصفية حسب الصنف',
                'filter_by_tag' => 'تصفية حسب العلامة',
                'sort_by' => 'ترتيب حسب',
                'sort_newest' => 'الأحدث',
                'sort_oldest' => 'الأقدم',
                'sort_most_viewed' => 'الأكثر مشاهدة',
                'sort_title' => 'العنوان',
                'clear_filters' => 'مسح التصفية',
                
                // الرسائل
                'success' => 'نجح',
                'error' => 'خطأ',
                'warning' => 'تحذير',
                'info' => 'معلومات',
                'confirm_delete' => 'هل أنت متأكد من الحذف؟',
                'action_cannot_undone' => 'لا يمكن التراجع عن هذا الإجراء',
                'yes_delete' => 'نعم، احذف',
                'no_cancel' => 'لا، إلغاء',
                
                // معلومات EXIF
                'camera_info' => 'معلومات الكاميرا',
                'camera_make' => 'الشركة المصنعة',
                'camera_model' => 'موديل الكاميرا',
                'lens_model' => 'موديل العدسة',
                'focal_length' => 'البعد البؤري',
                'aperture' => 'فتحة العدسة',
                'shutter_speed' => 'سرعة الغالق',
                'iso' => 'حساسية ISO',
                'flash' => 'الفلاش',
                'date_taken' => 'تاريخ التصوير',
                'gps_location' => 'الموقع الجغرافي',
                
                // الإحصائيات
                'statistics' => 'الإحصائيات',
                'total_images' => 'إجمالي الصور',
                'total_categories' => 'إجمالي الأصناف',
                'total_tags' => 'إجمالي العلامات',
                'total_views' => 'إجمالي المشاهدات',
                'recent_uploads' => 'الرفع الأخير',
                'popular_images' => 'الصور الشائعة'
            ],
            
            'en' => [
                // General
                'home' => 'Home',
                'gallery' => 'Gallery',
                'categories' => 'Categories',
                'tags' => 'Tags',
                'search' => 'Search',
                'admin' => 'Admin',
                'login' => 'Login',
                'logout' => 'Logout',
                'upload' => 'Upload',
                'edit' => 'Edit',
                'delete' => 'Delete',
                'save' => 'Save',
                'cancel' => 'Cancel',
                'back' => 'Back',
                'next' => 'Next',
                'previous' => 'Previous',
                'loading' => 'Loading...',
                'no_results' => 'No results found',
                'view_all' => 'View All',
                'show_more' => 'Show More',
                
                // Images
                'images' => 'Images',
                'image' => 'Image',
                'title' => 'Title',
                'description' => 'Description',
                'category' => 'Category',
                'upload_date' => 'Upload Date',
                'file_size' => 'File Size',
                'dimensions' => 'Dimensions',
                'views' => 'Views',
                'download' => 'Download',
                'share' => 'Share',
                'fullscreen' => 'Fullscreen',
                'slideshow' => 'Slideshow',
                
                // Categories
                'all_categories' => 'All Categories',
                'category_name' => 'Category Name',
                'category_description' => 'Category Description',
                'images_count' => 'Images Count',
                'add_category' => 'Add Category',
                'edit_category' => 'Edit Category',
                'delete_category' => 'Delete Category',
                
                // Tags
                'all_tags' => 'All Tags',
                'tag_name' => 'Tag Name',
                'usage_count' => 'Usage Count',
                'add_tag' => 'Add Tag',
                'edit_tag' => 'Edit Tag',
                'delete_tag' => 'Delete Tag',
                'popular_tags' => 'Popular Tags',
                
                // Upload
                'upload_images' => 'Upload Images',
                'select_files' => 'Select Files',
                'drag_drop' => 'Drag files here or click to select',
                'supported_formats' => 'Supported Formats',
                'max_file_size' => 'Max File Size',
                'upload_progress' => 'Upload Progress',
                'upload_complete' => 'Upload Complete',
                'upload_failed' => 'Upload Failed',
                
                // Search and Filter
                'search_placeholder' => 'Search images...',
                'filter_by_category' => 'Filter by Category',
                'filter_by_tag' => 'Filter by Tag',
                'sort_by' => 'Sort by',
                'sort_newest' => 'Newest',
                'sort_oldest' => 'Oldest',
                'sort_most_viewed' => 'Most Viewed',
                'sort_title' => 'Title',
                'clear_filters' => 'Clear Filters',
                
                // Messages
                'success' => 'Success',
                'error' => 'Error',
                'warning' => 'Warning',
                'info' => 'Info',
                'confirm_delete' => 'Are you sure you want to delete?',
                'action_cannot_undone' => 'This action cannot be undone',
                'yes_delete' => 'Yes, Delete',
                'no_cancel' => 'No, Cancel',
                
                // EXIF Info
                'camera_info' => 'Camera Info',
                'camera_make' => 'Camera Make',
                'camera_model' => 'Camera Model',
                'lens_model' => 'Lens Model',
                'focal_length' => 'Focal Length',
                'aperture' => 'Aperture',
                'shutter_speed' => 'Shutter Speed',
                'iso' => 'ISO',
                'flash' => 'Flash',
                'date_taken' => 'Date Taken',
                'gps_location' => 'GPS Location',
                
                // Statistics
                'statistics' => 'Statistics',
                'total_images' => 'Total Images',
                'total_categories' => 'Total Categories',
                'total_tags' => 'Total Tags',
                'total_views' => 'Total Views',
                'recent_uploads' => 'Recent Uploads',
                'popular_images' => 'Popular Images'
            ]
        ];
        
        return $translations[$lang] ?? $translations['en'];
    }
    
    /**
     * الحصول على ترجمة
     */
    public function get($key, $lang = null) {
        $lang = $lang ?: $this->currentLanguage;
        
        if (!in_array($lang, $this->loadedLanguages)) {
            $this->loadLanguage($lang);
        }
        
        return $this->translations[$lang][$key] ?? $key;
    }
    
    /**
     * تعيين اللغة الحالية
     */
    public function setLanguage($lang) {
        if (in_array($lang, SUPPORTED_LANGUAGES)) {
            $this->currentLanguage = $lang;
            if (!in_array($lang, $this->loadedLanguages)) {
                $this->loadLanguage($lang);
            }
            return true;
        }
        return false;
    }
    
    /**
     * الحصول على اللغة الحالية
     */
    public function getCurrentLanguage() {
        return $this->currentLanguage;
    }
    
    /**
     * الحصول على جميع الترجمات للغة معينة
     */
    public function getAll($lang = null) {
        $lang = $lang ?: $this->currentLanguage;
        
        if (!in_array($lang, $this->loadedLanguages)) {
            $this->loadLanguage($lang);
        }
        
        return $this->translations[$lang] ?? [];
    }
    
    /**
     * إضافة ترجمة جديدة
     */
    public function add($key, $value, $lang = null) {
        $lang = $lang ?: $this->currentLanguage;
        
        if (!in_array($lang, $this->loadedLanguages)) {
            $this->loadLanguage($lang);
        }
        
        $this->translations[$lang][$key] = $value;
    }
    
    /**
     * حفظ الترجمات في الملف
     */
    public function save($lang = null) {
        $lang = $lang ?: $this->currentLanguage;
        
        if (!isset($this->translations[$lang])) {
            return false;
        }
        
        $langFile = ROOT_PATH . "/languages/{$lang}.php";
        $content = "<?php\n";
        $content .= "/**\n";
        $content .= " * ملف ترجمة اللغة: {$lang}\n";
        $content .= " * Language file: {$lang}\n";
        $content .= " */\n\n";
        $content .= "return " . var_export($this->translations[$lang], true) . ";\n";
        
        return file_put_contents($langFile, $content) !== false;
    }
}

/**
 * دالة مساعدة للحصول على ترجمة
 */
function __($key, $lang = null) {
    return Language::getInstance()->get($key, $lang);
}

/**
 * دالة مساعدة للحصول على ترجمة مع متغيرات
 */
function __f($key, $vars = [], $lang = null) {
    $translation = Language::getInstance()->get($key, $lang);
    
    if (!empty($vars)) {
        foreach ($vars as $placeholder => $value) {
            $translation = str_replace('{' . $placeholder . '}', $value, $translation);
        }
    }
    
    return $translation;
}
?>
