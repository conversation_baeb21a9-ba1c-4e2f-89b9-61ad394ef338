<?php
/**
 * شريط التنقل المشترك للإدارة
 * Shared Admin Navigation
 */

// التأكد من تسجيل الدخول
if (!isset($currentUser)) {
    $auth = new Auth();
    if (!$auth->isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
    $currentUser = $auth->getCurrentUser();
}

// تحديد الصفحة النشطة
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
?>

<nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="index.php">
            <i class="fas fa-cog me-2"></i>
            <?php echo __('admin_panel'); ?>
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentPage === 'index' ? 'active' : ''; ?>" href="index.php">
                        <i class="fas fa-tachometer-alt me-1"></i><?php echo __('dashboard'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentPage === 'images' ? 'active' : ''; ?>" href="images.php">
                        <i class="fas fa-images me-1"></i><?php echo __('images'); ?>
                        <?php
                        // عرض عدد الصور
                        $imageCount = $db->count('images', 'is_active = 1');
                        if ($imageCount > 0):
                        ?>
                        <span class="badge bg-light text-dark ms-1"><?php echo $imageCount; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentPage === 'upload' ? 'active' : ''; ?>" href="upload.php">
                        <i class="fas fa-upload me-1"></i><?php echo __('upload'); ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentPage === 'categories' ? 'active' : ''; ?>" href="categories.php">
                        <i class="fas fa-folder me-1"></i><?php echo __('categories'); ?>
                        <?php
                        // عرض عدد الأصناف
                        $categoryCount = $db->count('categories');
                        if ($categoryCount > 0):
                        ?>
                        <span class="badge bg-light text-dark ms-1"><?php echo $categoryCount; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $currentPage === 'tags' ? 'active' : ''; ?>" href="tags.php">
                        <i class="fas fa-tags me-1"></i><?php echo __('tags'); ?>
                        <?php
                        // عرض عدد العلامات
                        $tagCount = $db->count('tags');
                        if ($tagCount > 0):
                        ?>
                        <span class="badge bg-light text-dark ms-1"><?php echo $tagCount; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-tools me-1"></i><?php echo __('tools'); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="backup.php">
                            <i class="fas fa-download me-2"></i><?php echo __('backup'); ?>
                        </a></li>
                        <li><a class="dropdown-item" href="cleanup.php">
                            <i class="fas fa-broom me-2"></i><?php echo __('cleanup'); ?>
                        </a></li>
                        <li><a class="dropdown-item" href="statistics.php">
                            <i class="fas fa-chart-bar me-2"></i><?php echo __('statistics'); ?>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="settings.php">
                            <i class="fas fa-cog me-2"></i><?php echo __('settings'); ?>
                        </a></li>
                    </ul>
                </li>
            </ul>
            
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="../index.php" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i><?php echo __('view_site'); ?>
                    </a>
                </li>
                
                <!-- Language Switcher -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        <?php echo strtoupper($currentLang); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?<?php echo http_build_query(array_merge($_GET, ['lang' => 'ar'])); ?>">
                            العربية
                        </a></li>
                        <li><a class="dropdown-item" href="?<?php echo http_build_query(array_merge($_GET, ['lang' => 'en'])); ?>">
                            English
                        </a></li>
                    </ul>
                </li>
                
                <!-- User Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo htmlspecialchars($currentUser['full_name'] ?: $currentUser['username']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><h6 class="dropdown-header">
                            <?php echo htmlspecialchars($currentUser['full_name'] ?: $currentUser['username']); ?>
                            <br><small class="text-muted"><?php echo htmlspecialchars($currentUser['email']); ?></small>
                        </h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="profile.php">
                            <i class="fas fa-user-edit me-2"></i><?php echo __('profile'); ?>
                        </a></li>
                        <li><a class="dropdown-item" href="change-password.php">
                            <i class="fas fa-key me-2"></i><?php echo __('change_password'); ?>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout'); ?>
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Quick Actions (Floating Action Buttons) -->
<div class="quick-actions d-none d-lg-block">
    <a href="upload.php" class="btn btn-primary" title="<?php echo __('upload_images'); ?>">
        <i class="fas fa-plus"></i>
    </a>
</div>

<script>
// تحديث الإشعارات في الوقت الفعلي (اختياري)
function updateNotifications() {
    // يمكن إضافة AJAX لجلب الإشعارات الجديدة
    fetch('ajax/get-notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.notifications && data.notifications.length > 0) {
                // عرض الإشعارات
                console.log('New notifications:', data.notifications);
            }
        })
        .catch(error => {
            console.error('Error fetching notifications:', error);
        });
}

// تحديث الإشعارات كل 5 دقائق
setInterval(updateNotifications, 300000);

// تحديث الوقت في الوقت الفعلي
function updateTime() {
    const timeElements = document.querySelectorAll('.live-time');
    timeElements.forEach(element => {
        const now = new Date();
        const options = {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        };
        element.textContent = now.toLocaleTimeString('<?php echo $currentLang; ?>', options);
    });
}

// تحديث الوقت كل ثانية
setInterval(updateTime, 1000);
updateTime(); // تحديث فوري

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات الحركة للقوائم
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        dropdown.addEventListener('show.bs.dropdown', function() {
            this.querySelector('.dropdown-menu').classList.add('fade-in-up');
        });
    });
    
    // تحسين الروابط النشطة
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    navLinks.forEach(link => {
        if (link.href === window.location.href) {
            link.classList.add('active');
        }
    });
    
    // إضافة تأثير التحميل للروابط
    const internalLinks = document.querySelectorAll('a[href^="index.php"], a[href^="images.php"], a[href^="upload.php"], a[href^="categories.php"], a[href^="tags.php"]');
    internalLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (this.href && !this.target) {
                const spinner = document.createElement('i');
                spinner.className = 'fas fa-spinner fa-spin ms-1';
                this.appendChild(spinner);
            }
        });
    });
});
</script>
