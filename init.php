<?php
/**
 * ملف التهيئة الرئيسي
 * Main Initialization File
 */

// تعريف ثابت للوصول
define('GALLERY_ACCESS', true);

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تضمين ملفات التكوين
require_once 'config.php';
require_once 'database.php';

// تضمين الفئات الأساسية
require_once 'classes/ImageManager.php';
require_once 'classes/CategoryManager.php';
require_once 'classes/TagManager.php';
require_once 'classes/FileUploader.php';
require_once 'classes/ImageProcessor.php';
require_once 'classes/Pagination.php';
require_once 'classes/Language.php';
require_once 'classes/Auth.php';

// إعداد معالج الأخطاء
set_error_handler('customErrorHandler');
set_exception_handler('customExceptionHandler');

/**
 * معالج الأخطاء المخصص
 */
function customErrorHandler($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $errorTypes = [
        E_ERROR => 'Fatal Error',
        E_WARNING => 'Warning',
        E_PARSE => 'Parse Error',
        E_NOTICE => 'Notice',
        E_CORE_ERROR => 'Core Error',
        E_CORE_WARNING => 'Core Warning',
        E_COMPILE_ERROR => 'Compile Error',
        E_COMPILE_WARNING => 'Compile Warning',
        E_USER_ERROR => 'User Error',
        E_USER_WARNING => 'User Warning',
        E_USER_NOTICE => 'User Notice',
        E_STRICT => 'Strict Notice',
        E_RECOVERABLE_ERROR => 'Recoverable Error',
        E_DEPRECATED => 'Deprecated',
        E_USER_DEPRECATED => 'User Deprecated'
    ];
    
    $errorType = isset($errorTypes[$severity]) ? $errorTypes[$severity] : 'Unknown Error';
    $errorMessage = "[" . date('Y-m-d H:i:s') . "] {$errorType}: {$message} in {$file} on line {$line}" . PHP_EOL;
    
    if (LOG_ERRORS && defined('ERROR_LOG_FILE')) {
        file_put_contents(ERROR_LOG_FILE, $errorMessage, FILE_APPEND | LOCK_EX);
    }
    
    if (DEBUG_MODE && SHOW_ERRORS) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 4px;'>";
        echo "<strong>{$errorType}:</strong> {$message}<br>";
        echo "<small>File: {$file} | Line: {$line}</small>";
        echo "</div>";
    }
    
    return true;
}

/**
 * معالج الاستثناءات المخصص
 */
function customExceptionHandler($exception) {
    $message = "[" . date('Y-m-d H:i:s') . "] Uncaught Exception: " . $exception->getMessage() . 
               " in " . $exception->getFile() . " on line " . $exception->getLine() . PHP_EOL;
    
    if (LOG_ERRORS && defined('ERROR_LOG_FILE')) {
        file_put_contents(ERROR_LOG_FILE, $message, FILE_APPEND | LOCK_EX);
    }
    
    if (DEBUG_MODE && SHOW_ERRORS) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 4px;'>";
        echo "<strong>Uncaught Exception:</strong> " . $exception->getMessage() . "<br>";
        echo "<small>File: " . $exception->getFile() . " | Line: " . $exception->getLine() . "</small>";
        echo "</div>";
    } else {
        echo "<h1>خطأ في النظام</h1><p>حدث خطأ غير متوقع. يرجى المحاولة لاحقاً.</p>";
    }
}

/**
 * دالة للحصول على اللغة الحالية
 */
function getCurrentLanguage() {
    if (isset($_SESSION['language']) && in_array($_SESSION['language'], SUPPORTED_LANGUAGES)) {
        return $_SESSION['language'];
    }
    
    if (isset($_COOKIE['language']) && in_array($_COOKIE['language'], SUPPORTED_LANGUAGES)) {
        $_SESSION['language'] = $_COOKIE['language'];
        return $_COOKIE['language'];
    }
    
    // تحديد اللغة من المتصفح
    if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
        $browserLangs = explode(',', $_SERVER['HTTP_ACCEPT_LANGUAGE']);
        foreach ($browserLangs as $lang) {
            $lang = substr(trim($lang), 0, 2);
            if (in_array($lang, SUPPORTED_LANGUAGES)) {
                $_SESSION['language'] = $lang;
                return $lang;
            }
        }
    }
    
    return DEFAULT_LANGUAGE;
}

/**
 * دالة لتعيين اللغة
 */
function setLanguage($lang) {
    if (in_array($lang, SUPPORTED_LANGUAGES)) {
        $_SESSION['language'] = $lang;
        setcookie('language', $lang, time() + (86400 * 30), '/'); // 30 يوم
        return true;
    }
    return false;
}

/**
 * دالة للتحقق من اتجاه النص
 */
function isRTL($lang = null) {
    $lang = $lang ?: getCurrentLanguage();
    return in_array($lang, RTL_LANGUAGES);
}

/**
 * دالة لتنسيق التاريخ
 */
function formatDate($date, $format = null, $lang = null) {
    $lang = $lang ?: getCurrentLanguage();
    
    if (!$format) {
        $format = ($lang === 'ar') ? 'j F Y' : 'F j, Y';
    }
    
    $timestamp = is_string($date) ? strtotime($date) : $date;
    
    if ($lang === 'ar') {
        // أسماء الأشهر بالعربية
        $arabicMonths = [
            'January' => 'يناير', 'February' => 'فبراير', 'March' => 'مارس',
            'April' => 'أبريل', 'May' => 'مايو', 'June' => 'يونيو',
            'July' => 'يوليو', 'August' => 'أغسطس', 'September' => 'سبتمبر',
            'October' => 'أكتوبر', 'November' => 'نوفمبر', 'December' => 'ديسمبر'
        ];
        
        $formatted = date($format, $timestamp);
        return str_replace(array_keys($arabicMonths), array_values($arabicMonths), $formatted);
    }
    
    return date($format, $timestamp);
}

/**
 * دالة لتنسيق حجم الملف
 */
function formatFileSize($bytes, $precision = 2) {
    $lang = getCurrentLanguage();
    $units = ($lang === 'ar') ? 
        ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'] : 
        ['B', 'KB', 'MB', 'GB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * دالة لإنشاء URL آمن
 */
function createUrl($path = '', $params = []) {
    $url = rtrim(BASE_URL, '/') . '/' . ltrim($path, '/');
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    return $url;
}

/**
 * دالة للتحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * دالة لإنشاء رمز عشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * دالة لتنظيف النص
 */
function cleanText($text) {
    return htmlspecialchars(trim($text), ENT_QUOTES, 'UTF-8');
}

/**
 * دالة للحصول على عنوان IP الحقيقي
 */
function getRealIpAddr() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

/**
 * دالة لإعادة التوجيه
 */
function redirect($url, $permanent = false) {
    if (!headers_sent()) {
        header('Location: ' . $url, true, $permanent ? 301 : 302);
        exit;
    } else {
        echo "<script>window.location.href = '{$url}';</script>";
        exit;
    }
}

/**
 * دالة لعرض الرسائل
 */
function showMessage($message, $type = 'info') {
    $types = [
        'success' => 'alert-success',
        'error' => 'alert-danger', 
        'warning' => 'alert-warning',
        'info' => 'alert-info'
    ];
    
    $class = isset($types[$type]) ? $types[$type] : $types['info'];
    
    return "<div class='alert {$class} alert-dismissible fade show' role='alert'>
                {$message}
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}

// تهيئة اللغة
$currentLang = getCurrentLanguage();

// تهيئة قاعدة البيانات
try {
    $db = Database::getInstance();
} catch (Exception $e) {
    if (DEBUG_MODE) {
        die("Database initialization failed: " . $e->getMessage());
    } else {
        die("خطأ في تهيئة النظام");
    }
}

// تعيين المتغيرات العامة
$GLOBALS['db'] = $db;
$GLOBALS['current_lang'] = $currentLang;
$GLOBALS['is_rtl'] = isRTL($currentLang);
?>
