<?php
/**
 * خطوة 1: التحقق من المتطلبات
 * Step 1: Requirements Check
 */

$requirements = checkRequirements();
?>

<div class="text-center mb-4">
    <h3><i class="fas fa-list-check me-2"></i>التحقق من متطلبات النظام</h3>
    <p class="text-muted">سنتحقق من توفر جميع المتطلبات اللازمة لتشغيل معرض الصور</p>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">متطلبات النظام</h5>
    </div>
    <div class="card-body p-0">
        <div class="requirement-item">
            <div>
                <strong>إصدار PHP</strong>
                <br><small class="text-muted">يجب أن يكون 7.4 أو أحدث (الحالي: <?php echo PHP_VERSION; ?>)</small>
            </div>
            <div class="status-icon <?php echo $requirements['requirements']['php_version'] ? 'status-pass' : 'status-fail'; ?>">
                <i class="fas <?php echo $requirements['requirements']['php_version'] ? 'fa-check' : 'fa-times'; ?>"></i>
            </div>
        </div>
        
        <div class="requirement-item">
            <div>
                <strong>امتداد PDO</strong>
                <br><small class="text-muted">مطلوب للاتصال بقاعدة البيانات</small>
            </div>
            <div class="status-icon <?php echo $requirements['requirements']['pdo'] ? 'status-pass' : 'status-fail'; ?>">
                <i class="fas <?php echo $requirements['requirements']['pdo'] ? 'fa-check' : 'fa-times'; ?>"></i>
            </div>
        </div>
        
        <div class="requirement-item">
            <div>
                <strong>امتداد PDO MySQL</strong>
                <br><small class="text-muted">مطلوب للاتصال بقاعدة بيانات MySQL</small>
            </div>
            <div class="status-icon <?php echo $requirements['requirements']['pdo_mysql'] ? 'status-pass' : 'status-fail'; ?>">
                <i class="fas <?php echo $requirements['requirements']['pdo_mysql'] ? 'fa-check' : 'fa-times'; ?>"></i>
            </div>
        </div>
        
        <div class="requirement-item">
            <div>
                <strong>امتداد GD</strong>
                <br><small class="text-muted">مطلوب لمعالجة وتغيير حجم الصور</small>
            </div>
            <div class="status-icon <?php echo $requirements['requirements']['gd'] ? 'status-pass' : 'status-fail'; ?>">
                <i class="fas <?php echo $requirements['requirements']['gd'] ? 'fa-check' : 'fa-times'; ?>"></i>
            </div>
        </div>
        
        <div class="requirement-item">
            <div>
                <strong>امتداد EXIF</strong>
                <br><small class="text-muted">مطلوب لقراءة بيانات الصور</small>
            </div>
            <div class="status-icon <?php echo $requirements['requirements']['exif'] ? 'status-pass' : 'status-fail'; ?>">
                <i class="fas <?php echo $requirements['requirements']['exif'] ? 'fa-check' : 'fa-times'; ?>"></i>
            </div>
        </div>
        
        <div class="requirement-item">
            <div>
                <strong>امتداد JSON</strong>
                <br><small class="text-muted">مطلوب لمعالجة البيانات</small>
            </div>
            <div class="status-icon <?php echo $requirements['requirements']['json'] ? 'status-pass' : 'status-fail'; ?>">
                <i class="fas <?php echo $requirements['requirements']['json'] ? 'fa-check' : 'fa-times'; ?>"></i>
            </div>
        </div>
        
        <div class="requirement-item">
            <div>
                <strong>امتداد mbstring</strong>
                <br><small class="text-muted">مطلوب للنصوص متعددة البايت</small>
            </div>
            <div class="status-icon <?php echo $requirements['requirements']['mbstring'] ? 'status-pass' : 'status-fail'; ?>">
                <i class="fas <?php echo $requirements['requirements']['mbstring'] ? 'fa-check' : 'fa-times'; ?>"></i>
            </div>
        </div>
        
        <div class="requirement-item">
            <div>
                <strong>مجلد uploads قابل للكتابة</strong>
                <br><small class="text-muted">مطلوب لحفظ الصور المرفوعة</small>
            </div>
            <div class="status-icon <?php echo $requirements['requirements']['uploads_writable'] ? 'status-pass' : 'status-fail'; ?>">
                <i class="fas <?php echo $requirements['requirements']['uploads_writable'] ? 'fa-check' : 'fa-times'; ?>"></i>
            </div>
        </div>
        
        <div class="requirement-item">
            <div>
                <strong>مجلد config قابل للكتابة</strong>
                <br><small class="text-muted">مطلوب لحفظ ملف الإعدادات</small>
            </div>
            <div class="status-icon <?php echo $requirements['requirements']['config_writable'] ? 'status-pass' : 'status-fail'; ?>">
                <i class="fas <?php echo $requirements['requirements']['config_writable'] ? 'fa-check' : 'fa-times'; ?>"></i>
            </div>
        </div>
    </div>
</div>

<?php if ($requirements['all_passed']): ?>
<div class="alert alert-success mt-4">
    <h6><i class="fas fa-check-circle me-2"></i>ممتاز!</h6>
    <p class="mb-0">جميع المتطلبات متوفرة. يمكنك المتابعة للخطوة التالية.</p>
</div>

<div class="text-center mt-4">
    <form method="POST">
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-arrow-right me-2"></i>
            المتابعة للخطوة التالية
        </button>
    </form>
</div>

<?php else: ?>
<div class="alert alert-danger mt-4">
    <h6><i class="fas fa-exclamation-triangle me-2"></i>متطلبات غير مكتملة</h6>
    <p class="mb-2">يرجى إصلاح المشاكل التالية قبل المتابعة:</p>
    <ul class="mb-0">
        <?php foreach ($requirements['errors'] as $error): ?>
        <li><?php echo htmlspecialchars($error); ?></li>
        <?php endforeach; ?>
    </ul>
</div>

<div class="text-center mt-4">
    <button type="button" class="btn btn-outline-primary" onclick="location.reload()">
        <i class="fas fa-refresh me-2"></i>
        إعادة فحص المتطلبات
    </button>
</div>
<?php endif; ?>

<div class="card mt-4">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-info-circle me-2"></i>
            معلومات إضافية
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>معلومات الخادم</h6>
                <ul class="list-unstyled small">
                    <li><strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?></li>
                    <li><strong>إصدار PHP:</strong> <?php echo PHP_VERSION; ?></li>
                    <li><strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف'; ?></li>
                    <li><strong>الذاكرة المتاحة:</strong> <?php echo ini_get('memory_limit'); ?></li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>إعدادات الرفع</h6>
                <ul class="list-unstyled small">
                    <li><strong>حد رفع الملف:</strong> <?php echo ini_get('upload_max_filesize'); ?></li>
                    <li><strong>حد POST:</strong> <?php echo ini_get('post_max_size'); ?></li>
                    <li><strong>وقت التنفيذ الأقصى:</strong> <?php echo ini_get('max_execution_time'); ?>s</li>
                    <li><strong>وقت الإدخال الأقصى:</strong> <?php echo ini_get('max_input_time'); ?>s</li>
                </ul>
            </div>
        </div>
    </div>
</div>
