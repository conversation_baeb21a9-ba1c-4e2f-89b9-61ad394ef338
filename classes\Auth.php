<?php
/**
 * فئة المصادقة والتحقق
 * Authentication Class
 */

class Auth {
    private $db;
    private $sessionTimeout;
    private $maxLoginAttempts;
    private $lockoutTime;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->sessionTimeout = SESSION_TIMEOUT;
        $this->maxLoginAttempts = MAX_LOGIN_ATTEMPTS;
        $this->lockoutTime = LOGIN_LOCKOUT_TIME;
    }
    
    /**
     * تسجيل الدخول
     */
    public function login($username, $password, $rememberMe = false) {
        // التحقق من محاولات تسجيل الدخول
        if ($this->isAccountLocked($username)) {
            return [
                'success' => false,
                'message' => 'الحساب مقفل مؤقتاً بسبب محاولات تسجيل دخول متعددة فاشلة'
            ];
        }
        
        // البحث عن المستخدم
        $user = $this->db->selectOne(
            "SELECT * FROM admin_users WHERE (username = ? OR email = ?) AND is_active = 1",
            [$username, $username]
        );
        
        if (!$user || !password_verify($password, $user['password_hash'])) {
            $this->recordFailedLogin($username);
            return [
                'success' => false,
                'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
            ];
        }
        
        // تسجيل الدخول الناجح
        $this->clearFailedLogins($username);
        $this->createSession($user, $rememberMe);
        $this->updateLastLogin($user['id']);
        
        return [
            'success' => true,
            'message' => 'تم تسجيل الدخول بنجاح',
            'user' => $user
        ];
    }
    
    /**
     * تسجيل الخروج
     */
    public function logout() {
        // حذف الجلسة
        if (isset($_SESSION['admin_user'])) {
            unset($_SESSION['admin_user']);
        }
        
        if (isset($_SESSION['admin_login_time'])) {
            unset($_SESSION['admin_login_time']);
        }
        
        // حذف كوكيز "تذكرني"
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
            
            // حذف الرمز من قاعدة البيانات
            $this->db->update(
                "UPDATE admin_users SET remember_token = NULL WHERE remember_token = ?",
                [$_COOKIE['remember_token']]
            );
        }
        
        return true;
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    public function isLoggedIn() {
        // التحقق من الجلسة
        if (isset($_SESSION['admin_user']) && isset($_SESSION['admin_login_time'])) {
            // التحقق من انتهاء صلاحية الجلسة
            if (time() - $_SESSION['admin_login_time'] > $this->sessionTimeout) {
                $this->logout();
                return false;
            }
            
            // تحديث وقت آخر نشاط
            $_SESSION['admin_login_time'] = time();
            return true;
        }
        
        // التحقق من كوكيز "تذكرني"
        if (isset($_COOKIE['remember_token'])) {
            return $this->loginWithRememberToken($_COOKIE['remember_token']);
        }
        
        return false;
    }
    
    /**
     * الحصول على المستخدم الحالي
     */
    public function getCurrentUser() {
        if ($this->isLoggedIn() && isset($_SESSION['admin_user'])) {
            return $_SESSION['admin_user'];
        }
        return null;
    }
    
    /**
     * إنشاء جلسة جديدة
     */
    private function createSession($user, $rememberMe = false) {
        $_SESSION['admin_user'] = [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'full_name' => $user['full_name']
        ];
        $_SESSION['admin_login_time'] = time();
        
        // إنشاء رمز "تذكرني" إذا طُلب
        if ($rememberMe) {
            $this->createRememberToken($user['id']);
        }
    }
    
    /**
     * إنشاء رمز "تذكرني"
     */
    private function createRememberToken($userId) {
        $token = bin2hex(random_bytes(32));
        $hashedToken = password_hash($token, PASSWORD_DEFAULT);
        
        // حفظ الرمز في قاعدة البيانات
        $this->db->update(
            "UPDATE admin_users SET remember_token = ? WHERE id = ?",
            [$hashedToken, $userId]
        );
        
        // إنشاء الكوكيز (صالح لمدة 30 يوم)
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
    }
    
    /**
     * تسجيل الدخول باستخدام رمز "تذكرني"
     */
    private function loginWithRememberToken($token) {
        $users = $this->db->select(
            "SELECT * FROM admin_users WHERE remember_token IS NOT NULL AND is_active = 1"
        );
        
        foreach ($users as $user) {
            if (password_verify($token, $user['remember_token'])) {
                $this->createSession($user, false);
                $this->updateLastLogin($user['id']);
                return true;
            }
        }
        
        // حذف الكوكيز إذا لم يتم العثور على رمز صحيح
        setcookie('remember_token', '', time() - 3600, '/');
        return false;
    }
    
    /**
     * تسجيل محاولة تسجيل دخول فاشلة
     */
    private function recordFailedLogin($username) {
        $ip = getRealIpAddr();
        $this->db->insert(
            "INSERT INTO login_attempts (username, ip_address, attempt_time) VALUES (?, ?, NOW())",
            [$username, $ip]
        );
    }
    
    /**
     * مسح محاولات تسجيل الدخول الفاشلة
     */
    private function clearFailedLogins($username) {
        $ip = getRealIpAddr();
        $this->db->delete(
            "DELETE FROM login_attempts WHERE username = ? OR ip_address = ?",
            [$username, $ip]
        );
    }
    
    /**
     * التحقق من قفل الحساب
     */
    private function isAccountLocked($username) {
        $ip = getRealIpAddr();
        $lockoutTime = date('Y-m-d H:i:s', time() - $this->lockoutTime);
        
        $attempts = $this->db->count(
            "login_attempts",
            "(username = ? OR ip_address = ?) AND attempt_time > ?",
            [$username, $ip, $lockoutTime]
        );
        
        return $attempts >= $this->maxLoginAttempts;
    }
    
    /**
     * تحديث وقت آخر تسجيل دخول
     */
    private function updateLastLogin($userId) {
        $this->db->update(
            "UPDATE admin_users SET last_login = NOW() WHERE id = ?",
            [$userId]
        );
    }
    
    /**
     * تغيير كلمة المرور
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        // التحقق من كلمة المرور الحالية
        $user = $this->db->selectOne("SELECT password_hash FROM admin_users WHERE id = ?", [$userId]);
        
        if (!$user || !password_verify($currentPassword, $user['password_hash'])) {
            return [
                'success' => false,
                'message' => 'كلمة المرور الحالية غير صحيحة'
            ];
        }
        
        // تحديث كلمة المرور
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $result = $this->db->update(
            "UPDATE admin_users SET password_hash = ?, updated_at = NOW() WHERE id = ?",
            [$hashedPassword, $userId]
        );
        
        if ($result) {
            return [
                'success' => true,
                'message' => 'تم تغيير كلمة المرور بنجاح'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'فشل في تغيير كلمة المرور'
            ];
        }
    }
    
    /**
     * إنشاء مستخدم جديد
     */
    public function createUser($data) {
        // التحقق من عدم وجود المستخدم
        $existing = $this->db->selectOne(
            "SELECT id FROM admin_users WHERE username = ? OR email = ?",
            [$data['username'], $data['email']]
        );
        
        if ($existing) {
            return [
                'success' => false,
                'message' => 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل'
            ];
        }
        
        // إنشاء المستخدم
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        $userId = $this->db->insert(
            "INSERT INTO admin_users (username, email, password_hash, full_name, is_active) VALUES (?, ?, ?, ?, ?)",
            [
                $data['username'],
                $data['email'],
                $hashedPassword,
                $data['full_name'] ?? '',
                $data['is_active'] ?? 1
            ]
        );
        
        if ($userId) {
            return [
                'success' => true,
                'message' => 'تم إنشاء المستخدم بنجاح',
                'user_id' => $userId
            ];
        } else {
            return [
                'success' => false,
                'message' => 'فشل في إنشاء المستخدم'
            ];
        }
    }
    
    /**
     * تنظيف محاولات تسجيل الدخول القديمة
     */
    public function cleanupOldLoginAttempts() {
        $cutoffTime = date('Y-m-d H:i:s', time() - (24 * 60 * 60)); // 24 ساعة
        return $this->db->delete(
            "DELETE FROM login_attempts WHERE attempt_time < ?",
            [$cutoffTime]
        );
    }
    
    /**
     * إنشاء جدول محاولات تسجيل الدخول إذا لم يكن موجوداً
     */
    public function createLoginAttemptsTable() {
        $query = "CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(100) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_username (username),
            INDEX idx_ip (ip_address),
            INDEX idx_time (attempt_time)
        )";
        
        return $this->db->execute($query);
    }
}
?>
