<?php
/**
 * صفحة إدارة الصور
 * Images Management Page
 */

require_once '../init.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// إنشاء كائنات الإدارة
$imageManager = new ImageManager();
$categoryManager = new CategoryManager();

$message = '';
$messageType = '';

// معالجة الحذف
if (isset($_POST['delete_image'])) {
    $imageId = (int)$_POST['image_id'];
    if ($imageManager->deleteImage($imageId)) {
        $message = __('image_deleted_successfully');
        $messageType = 'success';
    } else {
        $message = __('failed_to_delete_image');
        $messageType = 'danger';
    }
}

// معالجة الحذف المتعدد
if (isset($_POST['bulk_delete']) && !empty($_POST['selected_images'])) {
    $deletedCount = 0;
    foreach ($_POST['selected_images'] as $imageId) {
        if ($imageManager->deleteImage((int)$imageId)) {
            $deletedCount++;
        }
    }
    
    if ($deletedCount > 0) {
        $message = __('images_deleted_successfully') . ' (' . $deletedCount . ')';
        $messageType = 'success';
    } else {
        $message = __('failed_to_delete_images');
        $messageType = 'danger';
    }
}

// الحصول على المعاملات
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$categoryId = isset($_GET['category']) ? (int)$_GET['category'] : null;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'newest';

// إعداد التصفية
$filters = [];
if ($categoryId) $filters['category_id'] = $categoryId;
if ($search) $filters['search'] = $search;
if ($sort) $filters['sort'] = $sort;

// الحصول على الصور
$images = $imageManager->getImages($filters, $page, ADMIN_IMAGES_PER_PAGE);
$totalImages = $imageManager->countImages($filters);

// إنشاء التصفح
$pagination = new Pagination($totalImages, ADMIN_IMAGES_PER_PAGE, $page, 'images.php', $_GET);

// الحصول على الأصناف للتصفية
$categories = $categoryManager->getCategories();

$pageTitle = __('manage_images') . META_TITLE_SEPARATOR . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="<?php echo $currentLang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
    <?php if ($isRTL): ?>
    <link href="../assets/css/rtl.css" rel="stylesheet">
    <link href="assets/css/admin-rtl.css" rel="stylesheet">
    <?php endif; ?>
</head>
<body class="admin-body">
    <!-- Navigation -->
    <?php include 'includes/nav.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1"><?php echo __('manage_images'); ?></h1>
                <p class="text-muted mb-0"><?php echo $totalImages; ?> <?php echo __('total_images'); ?></p>
            </div>
            <div>
                <a href="upload.php" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i><?php echo __('upload_images'); ?>
                </a>
            </div>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="images.php" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label"><?php echo __('search'); ?></label>
                        <input type="text" 
                               class="form-control" 
                               id="search" 
                               name="search" 
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="<?php echo __('search_images'); ?>">
                    </div>
                    
                    <div class="col-md-3">
                        <label for="category" class="form-label"><?php echo __('category'); ?></label>
                        <select class="form-select" id="category" name="category">
                            <option value=""><?php echo __('all_categories'); ?></option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo $categoryId == $category['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($currentLang === 'ar' ? $category['name_ar'] : $category['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="sort" class="form-label"><?php echo __('sort_by'); ?></label>
                        <select class="form-select" id="sort" name="sort">
                            <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>><?php echo __('newest_first'); ?></option>
                            <option value="oldest" <?php echo $sort === 'oldest' ? 'selected' : ''; ?>><?php echo __('oldest_first'); ?></option>
                            <option value="title" <?php echo $sort === 'title' ? 'selected' : ''; ?>><?php echo __('title'); ?></option>
                            <option value="most_viewed" <?php echo $sort === 'most_viewed' ? 'selected' : ''; ?>><?php echo __('most_viewed'); ?></option>
                        </select>
                    </div>
                    
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i><?php echo __('filter'); ?>
                        </button>
                        <a href="images.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Images Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-images me-2"></i><?php echo __('images_list'); ?>
                </h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="bulk-delete-btn" disabled>
                        <i class="fas fa-trash me-1"></i><?php echo __('delete_selected'); ?>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($images)): ?>
                <form method="POST" id="bulk-form">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" class="form-check-input" id="select-all">
                                    </th>
                                    <th width="80"><?php echo __('thumbnail'); ?></th>
                                    <th><?php echo __('title'); ?></th>
                                    <th><?php echo __('category'); ?></th>
                                    <th><?php echo __('size'); ?></th>
                                    <th><?php echo __('views'); ?></th>
                                    <th><?php echo __('upload_date'); ?></th>
                                    <th width="120"><?php echo __('actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($images as $image): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" 
                                               class="form-check-input image-checkbox" 
                                               name="selected_images[]" 
                                               value="<?php echo $image['id']; ?>">
                                    </td>
                                    <td>
                                        <img src="<?php echo '../' . THUMBNAILS_DIR . basename($image['thumbnail_path']); ?>" 
                                             class="img-thumbnail" 
                                             style="width: 60px; height: 60px; object-fit: cover;"
                                             alt="<?php echo htmlspecialchars($currentLang === 'ar' ? $image['alt_text_ar'] : $image['alt_text']); ?>">
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($currentLang === 'ar' ? $image['title_ar'] : $image['title']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($image['original_filename']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($image['category_name']): ?>
                                        <span class="badge bg-secondary">
                                            <?php echo htmlspecialchars($currentLang === 'ar' ? $image['category_name_ar'] : $image['category_name']); ?>
                                        </span>
                                        <?php else: ?>
                                        <span class="text-muted"><?php echo __('no_category'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <?php echo formatFileSize($image['file_size']); ?>
                                            <br>
                                            <small class="text-muted"><?php echo $image['width']; ?>×<?php echo $image['height']; ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <i class="fas fa-eye me-1"></i><?php echo number_format($image['views_count']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div>
                                            <?php echo formatDate($image['upload_date'], 'M j, Y'); ?>
                                            <br>
                                            <small class="text-muted"><?php echo formatDate($image['upload_date'], 'H:i'); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="../image.php?id=<?php echo $image['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               target="_blank"
                                               title="<?php echo __('view'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit-image.php?id=<?php echo $image['id']; ?>" 
                                               class="btn btn-sm btn-outline-secondary"
                                               title="<?php echo __('edit'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger delete-btn" 
                                                    data-image-id="<?php echo $image['id']; ?>"
                                                    data-image-title="<?php echo htmlspecialchars($currentLang === 'ar' ? $image['title_ar'] : $image['title']); ?>"
                                                    title="<?php echo __('delete'); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Hidden form for bulk delete -->
                    <input type="hidden" name="bulk_delete" value="1">
                </form>
                
                <!-- Pagination -->
                <div class="card-footer">
                    <?php echo $pagination->render(['show_info' => true]); ?>
                </div>
                
                <?php else: ?>
                <!-- No Images -->
                <div class="text-center py-5">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h4><?php echo __('no_images_found'); ?></h4>
                    <p class="text-muted"><?php echo __('no_images_description'); ?></p>
                    <a href="upload.php" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i><?php echo __('upload_first_image'); ?>
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        <?php echo __('confirm_delete'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p><?php echo __('delete_image_confirmation'); ?></p>
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo __('action_cannot_undone'); ?>
                    </div>
                    <div id="image-to-delete"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i><?php echo __('cancel'); ?>
                    </button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="image_id" id="delete-image-id">
                        <button type="submit" name="delete_image" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i><?php echo __('yes_delete'); ?>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Delete Confirmation Modal -->
    <div class="modal fade" id="bulkDeleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        <?php echo __('confirm_bulk_delete'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p><?php echo __('bulk_delete_confirmation'); ?></p>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo __('bulk_delete_warning'); ?>
                    </div>
                    <div id="selected-count"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i><?php echo __('cancel'); ?>
                    </button>
                    <button type="button" class="btn btn-danger" id="confirm-bulk-delete">
                        <i class="fas fa-trash me-1"></i><?php echo __('yes_delete_all'); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/gallery.js"></script>
    <script src="assets/js/admin.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Select all checkbox
            const selectAllCheckbox = document.getElementById('select-all');
            const imageCheckboxes = document.querySelectorAll('.image-checkbox');
            const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
            
            selectAllCheckbox.addEventListener('change', function() {
                imageCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkDeleteButton();
            });
            
            imageCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectAllCheckbox();
                    updateBulkDeleteButton();
                });
            });
            
            function updateSelectAllCheckbox() {
                const checkedCount = document.querySelectorAll('.image-checkbox:checked').length;
                selectAllCheckbox.checked = checkedCount === imageCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < imageCheckboxes.length;
            }
            
            function updateBulkDeleteButton() {
                const checkedCount = document.querySelectorAll('.image-checkbox:checked').length;
                bulkDeleteBtn.disabled = checkedCount === 0;
                bulkDeleteBtn.innerHTML = `<i class="fas fa-trash me-1"></i><?php echo __('delete_selected'); ?> (${checkedCount})`;
            }
            
            // Delete single image
            document.querySelectorAll('.delete-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const imageId = this.dataset.imageId;
                    const imageTitle = this.dataset.imageTitle;
                    
                    document.getElementById('delete-image-id').value = imageId;
                    document.getElementById('image-to-delete').innerHTML = 
                        `<strong><?php echo __('image'); ?>:</strong> ${imageTitle}`;
                    
                    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
                    modal.show();
                });
            });
            
            // Bulk delete
            bulkDeleteBtn.addEventListener('click', function() {
                const checkedCount = document.querySelectorAll('.image-checkbox:checked').length;
                if (checkedCount === 0) return;
                
                document.getElementById('selected-count').innerHTML = 
                    `<strong>${checkedCount}</strong> <?php echo __('images_selected'); ?>`;
                
                const modal = new bootstrap.Modal(document.getElementById('bulkDeleteModal'));
                modal.show();
            });
            
            document.getElementById('confirm-bulk-delete').addEventListener('click', function() {
                document.getElementById('bulk-form').submit();
            });
        });
    </script>
</body>
</html>
