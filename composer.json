{"name": "photo-gallery/multilingual-gallery", "description": "معرض صور احترافي متعدد اللغات مع لوحة إدارة متكاملة | Professional multilingual photo gallery with complete admin panel", "type": "project", "keywords": ["photo-gallery", "multilingual", "arabic", "english", "rtl", "bootstrap", "php", "mysql", "responsive", "admin-panel"], "homepage": "https://github.com/your-repo/photo-gallery", "license": "MIT", "authors": [{"name": "Your Name", "email": "<EMAIL>", "role": "Developer"}], "support": {"issues": "https://github.com/your-repo/photo-gallery/issues", "source": "https://github.com/your-repo/photo-gallery", "docs": "https://github.com/your-repo/photo-gallery/wiki"}, "require": {"php": ">=7.4.0", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-gd": "*", "ext-exif": "*", "ext-json": "*", "ext-mbstring": "*", "ext-fileinfo": "*"}, "require-dev": {"phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.6", "phpstan/phpstan": "^1.0"}, "suggest": {"ext-imagick": "For advanced image processing capabilities", "ext-redis": "For session storage and caching", "ext-memcached": "For caching support", "monolog/monolog": "For advanced logging capabilities"}, "autoload": {"psr-4": {"PhotoGallery\\": "classes/"}, "files": ["includes/functions.php"]}, "autoload-dev": {"psr-4": {"PhotoGallery\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "cs-check": "phpcs --standard=PSR12 classes/ admin/ --ignore=*/vendor/*", "cs-fix": "phpcbf --standard=PSR12 classes/ admin/ --ignore=*/vendor/*", "analyze": "phpstan analyse classes/ admin/ --level=5", "install-hooks": ["cp scripts/pre-commit .git/hooks/", "chmod +x .git/hooks/pre-commit"], "post-install-cmd": ["@install-hooks"], "post-update-cmd": ["@install-hooks"]}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"composer/installers": true}}, "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "minimum-stability": "stable", "prefer-stable": true}