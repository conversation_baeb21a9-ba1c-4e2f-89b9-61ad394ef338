# ===================================
# ملف الإعدادات البيئية لمعرض الصور
# Photo Gallery Environment Configuration
# ===================================

# انسخ هذا الملف إلى .env وعدّل القيم حسب بيئتك
# Copy this file to .env and modify values for your environment

# ===================================
# إعدادات قاعدة البيانات
# Database Configuration
# ===================================
DB_HOST=localhost
DB_PORT=3306
DB_NAME=photo_gallery
DB_USER=gallery_user
DB_PASS=your_secure_password
DB_CHARSET=utf8mb4

# ===================================
# إعدادات الموقع العامة
# General Site Settings
# ===================================
SITE_NAME="معرض الصور"
SITE_DESCRIPTION="معرض صور احترافي متعدد اللغات"
SITE_URL=http://localhost:8000
DEFAULT_LANGUAGE=ar
TIMEZONE=Asia/Riyadh

# ===================================
# إعدادات الأمان
# Security Settings
# ===================================
SECURITY_SALT=your_random_64_character_salt_here_change_this_value
DEBUG_MODE=false
SESSION_TIMEOUT=3600
REMEMBER_ME_DURATION=2592000
MAX_LOGIN_ATTEMPTS=5
LOGIN_LOCKOUT_TIME=900

# ===================================
# إعدادات الصور
# Image Settings
# ===================================
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp
THUMBNAIL_WIDTH=300
THUMBNAIL_HEIGHT=300
MEDIUM_WIDTH=800
MEDIUM_HEIGHT=600
WATERMARK_ENABLED=false
WATERMARK_TEXT=""
WATERMARK_OPACITY=50

# ===================================
# إعدادات العرض
# Display Settings
# ===================================
IMAGES_PER_PAGE=12
ADMIN_IMAGES_PER_PAGE=20
ENABLE_LIGHTBOX=true
ENABLE_SLIDESHOW=true
ENABLE_DOWNLOAD=true
ENABLE_SHARING=true

# ===================================
# إعدادات SEO
# SEO Settings
# ===================================
META_TITLE_SEPARATOR=" | "
DEFAULT_META_DESCRIPTION="معرض صور احترافي متعدد اللغات"
DEFAULT_META_KEYWORDS="صور,معرض,تصوير,فوتوغرافيا"
ENABLE_SITEMAP=true
ENABLE_ROBOTS=true

# ===================================
# إعدادات التخزين المؤقت
# Caching Settings
# ===================================
CACHE_ENABLED=true
CACHE_DRIVER=file
CACHE_LIFETIME=3600
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=""
REDIS_DATABASE=0

# ===================================
# إعدادات البريد الإلكتروني
# Email Settings
# ===================================
MAIL_ENABLED=false
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="معرض الصور"

# ===================================
# إعدادات التحليلات
# Analytics Settings
# ===================================
GOOGLE_ANALYTICS_ID=""
FACEBOOK_PIXEL_ID=""
ENABLE_VISITOR_TRACKING=true

# ===================================
# إعدادات المشاركة الاجتماعية
# Social Sharing Settings
# ===================================
FACEBOOK_APP_ID=""
TWITTER_SITE=""
ENABLE_SOCIAL_LOGIN=false

# ===================================
# إعدادات النسخ الاحتياطي
# Backup Settings
# ===================================
AUTO_BACKUP_ENABLED=true
BACKUP_FREQUENCY=daily
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=backups/
BACKUP_INCLUDE_IMAGES=true

# ===================================
# إعدادات الأداء
# Performance Settings
# ===================================
ENABLE_COMPRESSION=true
ENABLE_BROWSER_CACHING=true
OPTIMIZE_IMAGES=true
LAZY_LOADING=true
CDN_ENABLED=false
CDN_URL=""

# ===================================
# إعدادات التطوير
# Development Settings
# ===================================
LOG_LEVEL=info
LOG_QUERIES=false
ENABLE_PROFILER=false
SHOW_DEBUG_BAR=false

# ===================================
# إعدادات الخدمات الخارجية
# External Services Settings
# ===================================
# خدمة تحسين الصور
TINYPNG_API_KEY=""
CLOUDINARY_CLOUD_NAME=""
CLOUDINARY_API_KEY=""
CLOUDINARY_API_SECRET=""

# خدمة الترجمة التلقائية
GOOGLE_TRANSLATE_API_KEY=""

# خدمة الخرائط
GOOGLE_MAPS_API_KEY=""

# ===================================
# إعدادات الأمان المتقدمة
# Advanced Security Settings
# ===================================
ENABLE_2FA=false
ENABLE_CAPTCHA=false
RECAPTCHA_SITE_KEY=""
RECAPTCHA_SECRET_KEY=""
ENABLE_IP_WHITELIST=false
ALLOWED_IPS="127.0.0.1,::1"

# ===================================
# إعدادات API
# API Settings
# ===================================
API_ENABLED=false
API_RATE_LIMIT=100
API_RATE_LIMIT_WINDOW=3600
JWT_SECRET=your_jwt_secret_key_here
API_VERSION=v1

# ===================================
# إعدادات الإشعارات
# Notification Settings
# ===================================
ENABLE_NOTIFICATIONS=true
SLACK_WEBHOOK_URL=""
DISCORD_WEBHOOK_URL=""
TELEGRAM_BOT_TOKEN=""
TELEGRAM_CHAT_ID=""

# ===================================
# إعدادات المراقبة
# Monitoring Settings
# ===================================
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_TOKEN=""
SENTRY_DSN=""
NEW_RELIC_LICENSE_KEY=""

# ===================================
# إعدادات التخزين السحابي
# Cloud Storage Settings
# ===================================
CLOUD_STORAGE_ENABLED=false
CLOUD_STORAGE_DRIVER=s3
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=""
AWS_URL=""

# ===================================
# إعدادات إضافية
# Additional Settings
# ===================================
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="الموقع تحت الصيانة"
ENABLE_REGISTRATION=false
ENABLE_COMMENTS=false
ENABLE_RATINGS=false
ENABLE_FAVORITES=false
