/**
 * أنماط دعم اللغة العربية والاتجاه من اليمين لليسار
 * RTL (Right-to-Left) Support Styles
 */

/* ===== الخطوط العربية ===== */
body[dir="rtl"] {
    font-family: 'Segoe UI', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Helvetica Neue', sans-serif;
    text-align: right;
}

/* ===== التنقل ===== */
.navbar-brand[dir="rtl"] {
    margin-right: 0;
    margin-left: auto;
}

.navbar-nav[dir="rtl"] .nav-link {
    padding-right: 0.5rem;
    padding-left: 1rem;
}

.navbar-toggler[dir="rtl"] {
    margin-left: 0;
    margin-right: auto;
}

/* ===== الأزرار والأيقونات ===== */
[dir="rtl"] .btn .fas,
[dir="rtl"] .btn .far,
[dir="rtl"] .btn .fab {
    margin-right: 0;
    margin-left: 0.5rem;
}

[dir="rtl"] .btn .fas:first-child,
[dir="rtl"] .btn .far:first-child,
[dir="rtl"] .btn .fab:first-child {
    margin-right: 0.5rem;
    margin-left: 0;
}

/* ===== القوائم المنسدلة ===== */
[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

[dir="rtl"] .dropdown-toggle::after {
    margin-left: 0;
    margin-right: 0.255em;
}

/* ===== النماذج ===== */
[dir="rtl"] .form-control {
    text-align: right;
}

[dir="rtl"] .input-group .form-control:not(:last-child) {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

[dir="rtl"] .input-group .form-control:not(:first-child) {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* ===== البطاقات ===== */
[dir="rtl"] .card-header {
    text-align: right;
}

[dir="rtl"] .card-body {
    text-align: right;
}

[dir="rtl"] .card-title {
    text-align: right;
}

/* ===== الشريط الجانبي ===== */
[dir="rtl"] .list-group-item {
    text-align: right;
}

[dir="rtl"] .list-group-item:hover {
    transform: translateX(-5px);
}

[dir="rtl"] .badge.float-end {
    float: left !important;
}

/* ===== معرض الصور ===== */
[dir="rtl"] .gallery-actions {
    direction: ltr;
}

[dir="rtl"] .image-actions-overlay {
    right: auto;
    left: 1rem;
}

/* ===== التصفح (Pagination) ===== */
[dir="rtl"] .pagination {
    direction: ltr;
}

[dir="rtl"] .page-link {
    text-align: center;
}

/* ===== مسار التنقل (Breadcrumb) ===== */
[dir="rtl"] .breadcrumb {
    direction: rtl;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    transform: scaleX(-1);
}

/* ===== العلامات ===== */
[dir="rtl"] .tag-cloud {
    text-align: right;
}

[dir="rtl"] .tag-list {
    text-align: right;
}

/* ===== الجداول ===== */
[dir="rtl"] .table {
    text-align: right;
}

[dir="rtl"] .table th,
[dir="rtl"] .table td {
    text-align: right;
}

/* ===== التنبيهات ===== */
[dir="rtl"] .alert {
    text-align: right;
}

[dir="rtl"] .alert .btn-close {
    margin-left: 0;
    margin-right: auto;
}

/* ===== النوافذ المنبثقة ===== */
[dir="rtl"] .modal-header {
    text-align: right;
}

[dir="rtl"] .modal-body {
    text-align: right;
}

[dir="rtl"] .modal-footer {
    justify-content: flex-start;
}

[dir="rtl"] .modal-footer .btn:first-child {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* ===== التبويبات ===== */
[dir="rtl"] .nav-tabs {
    border-bottom: 1px solid #dee2e6;
}

[dir="rtl"] .nav-tabs .nav-link {
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

/* ===== الأكورديون ===== */
[dir="rtl"] .accordion-button {
    text-align: right;
}

[dir="rtl"] .accordion-button::after {
    margin-left: 0;
    margin-right: auto;
}

/* ===== شريط التقدم ===== */
[dir="rtl"] .progress {
    direction: ltr;
}

/* ===== الأرقام والتواريخ ===== */
[dir="rtl"] .number,
[dir="rtl"] .date,
[dir="rtl"] .time {
    direction: ltr;
    text-align: left;
    display: inline-block;
}

/* ===== الروابط ===== */
[dir="rtl"] a {
    text-decoration: none;
}

[dir="rtl"] a:hover {
    text-decoration: underline;
}

/* ===== النصوص ===== */
[dir="rtl"] .text-left {
    text-align: right !important;
}

[dir="rtl"] .text-right {
    text-align: left !important;
}

[dir="rtl"] .float-left {
    float: right !important;
}

[dir="rtl"] .float-right {
    float: left !important;
}

/* ===== الهوامش والحشو ===== */
[dir="rtl"] .me-1 { margin-right: 0 !important; margin-left: 0.25rem !important; }
[dir="rtl"] .me-2 { margin-right: 0 !important; margin-left: 0.5rem !important; }
[dir="rtl"] .me-3 { margin-right: 0 !important; margin-left: 1rem !important; }
[dir="rtl"] .me-4 { margin-right: 0 !important; margin-left: 1.5rem !important; }
[dir="rtl"] .me-5 { margin-right: 0 !important; margin-left: 3rem !important; }

[dir="rtl"] .ms-1 { margin-left: 0 !important; margin-right: 0.25rem !important; }
[dir="rtl"] .ms-2 { margin-left: 0 !important; margin-right: 0.5rem !important; }
[dir="rtl"] .ms-3 { margin-left: 0 !important; margin-right: 1rem !important; }
[dir="rtl"] .ms-4 { margin-left: 0 !important; margin-right: 1.5rem !important; }
[dir="rtl"] .ms-5 { margin-left: 0 !important; margin-right: 3rem !important; }

[dir="rtl"] .pe-1 { padding-right: 0 !important; padding-left: 0.25rem !important; }
[dir="rtl"] .pe-2 { padding-right: 0 !important; padding-left: 0.5rem !important; }
[dir="rtl"] .pe-3 { padding-right: 0 !important; padding-left: 1rem !important; }
[dir="rtl"] .pe-4 { padding-right: 0 !important; padding-left: 1.5rem !important; }
[dir="rtl"] .pe-5 { padding-right: 0 !important; padding-left: 3rem !important; }

[dir="rtl"] .ps-1 { padding-left: 0 !important; padding-right: 0.25rem !important; }
[dir="rtl"] .ps-2 { padding-left: 0 !important; padding-right: 0.5rem !important; }
[dir="rtl"] .ps-3 { padding-left: 0 !important; padding-right: 1rem !important; }
[dir="rtl"] .ps-4 { padding-left: 0 !important; padding-right: 1.5rem !important; }
[dir="rtl"] .ps-5 { padding-left: 0 !important; padding-right: 3rem !important; }

/* ===== تحسينات خاصة بالعربية ===== */
[dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, 
[dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6 {
    font-weight: 600;
    line-height: 1.4;
}

[dir="rtl"] p {
    line-height: 1.8;
}

[dir="rtl"] .lead {
    font-size: 1.1rem;
    line-height: 1.7;
}

/* ===== تحسينات للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    [dir="rtl"] .navbar-collapse {
        text-align: right;
    }
    
    [dir="rtl"] .navbar-nav {
        text-align: right;
    }
    
    [dir="rtl"] .dropdown-menu {
        right: 0;
        left: auto;
    }
}

/* ===== تحسينات الطباعة ===== */
@media print {
    [dir="rtl"] body {
        font-family: 'Arial', sans-serif;
        direction: rtl;
        text-align: right;
    }
    
    [dir="rtl"] .container,
    [dir="rtl"] .container-fluid {
        direction: rtl;
    }
}

/* ===== تخصيصات إضافية ===== */
[dir="rtl"] .carousel-control-prev {
    right: 0;
    left: auto;
}

[dir="rtl"] .carousel-control-next {
    right: auto;
    left: 0;
}

[dir="rtl"] .carousel-indicators {
    direction: ltr;
}

/* ===== تحسينات الوصولية ===== */
[dir="rtl"] .sr-only {
    direction: rtl;
}

[dir="rtl"] .visually-hidden {
    direction: rtl;
}

/* ===== تخصيص المكونات المخصصة ===== */
[dir="rtl"] .gallery-item .card-body {
    text-align: right;
}

[dir="rtl"] .exif-data {
    text-align: right;
}

[dir="rtl"] .pagination-info {
    text-align: center;
}

/* ===== إصلاحات للمتصفحات القديمة ===== */
[dir="rtl"] .d-flex {
    direction: rtl;
}

[dir="rtl"] .justify-content-between {
    flex-direction: row-reverse;
}

[dir="rtl"] .justify-content-end {
    justify-content: flex-start !important;
}

[dir="rtl"] .justify-content-start {
    justify-content: flex-end !important;
}
