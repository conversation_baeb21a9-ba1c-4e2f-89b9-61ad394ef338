<?php
/**
 * فئة إدارة الأصناف
 * Category Management Class
 */

class CategoryManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع الأصناف
     */
    public function getCategories($includeCount = false) {
        if ($includeCount) {
            $query = "SELECT c.*, COUNT(i.id) as image_count 
                      FROM categories c 
                      LEFT JOIN images i ON c.id = i.category_id AND i.is_active = 1
                      GROUP BY c.id 
                      ORDER BY c.name";
        } else {
            $query = "SELECT * FROM categories ORDER BY name";
        }
        
        return $this->db->select($query);
    }
    
    /**
     * الحصول على الأصناف مع عدد الصور
     */
    public function getCategoriesWithImageCount() {
        return $this->getCategories(true);
    }
    
    /**
     * الحصول على صنف واحد
     */
    public function getCategory($id) {
        $query = "SELECT * FROM categories WHERE id = ?";
        return $this->db->selectOne($query, [$id]);
    }
    
    /**
     * الحصول على صنف بالـ slug
     */
    public function getCategoryBySlug($slug) {
        $query = "SELECT * FROM categories WHERE slug = ?";
        return $this->db->selectOne($query, [$slug]);
    }
    
    /**
     * إضافة صنف جديد
     */
    public function addCategory($data) {
        // التحقق من البيانات المطلوبة
        if (empty($data['name']) || empty($data['name_ar'])) {
            return false;
        }
        
        // إنشاء slug فريد
        $slug = $this->db->createUniqueSlug('categories', 'slug', $data['name']);
        
        $query = "INSERT INTO categories (name, name_ar, description, description_ar, slug) 
                  VALUES (?, ?, ?, ?, ?)";
        
        $params = [
            $data['name'],
            $data['name_ar'],
            $data['description'] ?? '',
            $data['description_ar'] ?? '',
            $slug
        ];
        
        return $this->db->insert($query, $params);
    }
    
    /**
     * تحديث صنف
     */
    public function updateCategory($id, $data) {
        $fields = [];
        $params = [];
        
        $allowedFields = ['name', 'name_ar', 'description', 'description_ar'];
        
        foreach ($allowedFields as $field) {
            if (array_key_exists($field, $data)) {
                $fields[] = "{$field} = ?";
                $params[] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            return false;
        }
        
        // تحديث الـ slug إذا تم تغيير الاسم
        if (isset($data['name'])) {
            $fields[] = "slug = ?";
            $params[] = $this->db->createUniqueSlug('categories', 'slug', $data['name'], $id);
        }
        
        $params[] = $id;
        $query = "UPDATE categories SET " . implode(', ', $fields) . ", updated_at = NOW() WHERE id = ?";
        
        return $this->db->update($query, $params);
    }
    
    /**
     * حذف صنف
     */
    public function deleteCategory($id) {
        // التحقق من وجود صور في هذا الصنف
        $imageCount = $this->db->count('images', 'category_id = ? AND is_active = 1', [$id]);
        
        if ($imageCount > 0) {
            // نقل الصور إلى "بدون صنف" بدلاً من حذف الصنف
            $this->db->update("UPDATE images SET category_id = NULL WHERE category_id = ?", [$id]);
        }
        
        $query = "DELETE FROM categories WHERE id = ?";
        return $this->db->delete($query, [$id]);
    }
    
    /**
     * الحصول على عدد الصور في صنف
     */
    public function getCategoryImageCount($categoryId) {
        return $this->db->count('images', 'category_id = ? AND is_active = 1', [$categoryId]);
    }
    
    /**
     * التحقق من وجود صنف بنفس الاسم
     */
    public function categoryExists($name, $excludeId = null) {
        $query = "SELECT id FROM categories WHERE (name = ? OR name_ar = ?)";
        $params = [$name, $name];
        
        if ($excludeId) {
            $query .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        return $this->db->selectOne($query, $params) !== false;
    }
    
    /**
     * البحث في الأصناف
     */
    public function searchCategories($searchTerm) {
        $searchTerm = '%' . $searchTerm . '%';
        
        $query = "SELECT c.*, COUNT(i.id) as image_count 
                  FROM categories c 
                  LEFT JOIN images i ON c.id = i.category_id AND i.is_active = 1
                  WHERE c.name LIKE ? OR c.name_ar LIKE ? OR c.description LIKE ? OR c.description_ar LIKE ?
                  GROUP BY c.id 
                  ORDER BY c.name";
        
        return $this->db->select($query, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    /**
     * الحصول على الأصناف الأكثر استخداماً
     */
    public function getPopularCategories($limit = 10) {
        $query = "SELECT c.*, COUNT(i.id) as image_count 
                  FROM categories c 
                  INNER JOIN images i ON c.id = i.category_id AND i.is_active = 1
                  GROUP BY c.id 
                  HAVING image_count > 0
                  ORDER BY image_count DESC, c.name 
                  LIMIT {$limit}";
        
        return $this->db->select($query);
    }
    
    /**
     * الحصول على إحصائيات الأصناف
     */
    public function getCategoryStats() {
        $query = "SELECT 
                    COUNT(*) as total_categories,
                    COUNT(CASE WHEN EXISTS(SELECT 1 FROM images WHERE category_id = categories.id AND is_active = 1) THEN 1 END) as categories_with_images,
                    COUNT(CASE WHEN NOT EXISTS(SELECT 1 FROM images WHERE category_id = categories.id AND is_active = 1) THEN 1 END) as empty_categories
                  FROM categories";
        
        return $this->db->selectOne($query);
    }
    
    /**
     * ترتيب الأصناف
     */
    public function reorderCategories($categoryIds) {
        if (empty($categoryIds)) {
            return false;
        }
        
        $this->db->beginTransaction();
        
        try {
            $query = "UPDATE categories SET sort_order = ? WHERE id = ?";
            
            foreach ($categoryIds as $order => $categoryId) {
                $this->db->update($query, [$order + 1, $categoryId]);
            }
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * تصدير الأصناف
     */
    public function exportCategories($format = 'json') {
        $categories = $this->getCategoriesWithImageCount();
        
        switch ($format) {
            case 'json':
                return json_encode($categories, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                
            case 'csv':
                $csv = "ID,Name,Name (Arabic),Description,Description (Arabic),Slug,Image Count,Created At\n";
                foreach ($categories as $category) {
                    $csv .= sprintf('"%s","%s","%s","%s","%s","%s","%s","%s"' . "\n",
                        $category['id'],
                        str_replace('"', '""', $category['name']),
                        str_replace('"', '""', $category['name_ar']),
                        str_replace('"', '""', $category['description']),
                        str_replace('"', '""', $category['description_ar']),
                        $category['slug'],
                        $category['image_count'],
                        $category['created_at']
                    );
                }
                return $csv;
                
            default:
                return $categories;
        }
    }
    
    /**
     * استيراد الأصناف
     */
    public function importCategories($data, $format = 'json') {
        if ($format === 'json') {
            $categories = json_decode($data, true);
        } else {
            return false; // CSV import can be implemented later
        }
        
        if (!is_array($categories)) {
            return false;
        }
        
        $this->db->beginTransaction();
        $imported = 0;
        
        try {
            foreach ($categories as $categoryData) {
                if (isset($categoryData['name']) && isset($categoryData['name_ar'])) {
                    // التحقق من عدم وجود الصنف
                    if (!$this->categoryExists($categoryData['name'])) {
                        if ($this->addCategory($categoryData)) {
                            $imported++;
                        }
                    }
                }
            }
            
            $this->db->commit();
            return $imported;
            
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
}
?>
