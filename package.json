{"name": "multilingual-photo-gallery", "version": "1.0.0", "description": "معرض صور احترافي متعدد اللغات | Professional multilingual photo gallery", "main": "assets/js/gallery.js", "scripts": {"build": "npm run build-css && npm run build-js", "build-css": "sass assets/scss:assets/css --style compressed", "build-js": "webpack --mode production", "dev": "npm run dev-css && npm run dev-js", "dev-css": "sass assets/scss:assets/css --watch", "dev-js": "webpack --mode development --watch", "lint": "eslint assets/js/**/*.js", "lint-fix": "eslint assets/js/**/*.js --fix", "test": "jest", "optimize-images": "imagemin assets/images/**/* --out-dir=assets/images/optimized", "start": "php -S localhost:8000", "deploy": "npm run build && npm run optimize-images"}, "keywords": ["photo-gallery", "multilingual", "arabic", "english", "rtl", "bootstrap", "responsive", "admin-panel", "php", "mysql"], "author": {"name": "Your Name", "email": "<EMAIL>", "url": "https://github.com/your-username"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/photo-gallery.git"}, "bugs": {"url": "https://github.com/your-repo/photo-gallery/issues"}, "homepage": "https://github.com/your-repo/photo-gallery#readme", "dependencies": {"bootstrap": "^5.3.0", "@fortawesome/fontawesome-free": "^6.4.0", "lightbox2": "^2.11.4", "dropzone": "^6.0.0-beta.2"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "babel-loader": "^9.1.0", "css-loader": "^6.8.0", "eslint": "^8.44.0", "eslint-config-standard": "^17.1.0", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-webp": "^7.0.0", "jest": "^29.6.0", "mini-css-extract-plugin": "^2.7.0", "sass": "^1.63.0", "sass-loader": "^13.3.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "files": ["assets/", "classes/", "admin/", "languages/", "includes/", "*.php", "*.md", "composer.json"], "directories": {"lib": "classes", "doc": "docs", "test": "tests"}}