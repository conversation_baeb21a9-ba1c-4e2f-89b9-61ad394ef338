-- إنشاء قاعدة البيانات لمعرض الصور
-- Photo Gallery Database Schema

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS photo_gallery CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE photo_gallery;

-- جدول الأصناف (Categories)
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    description TEXT,
    description_ar TEXT,
    slug VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_name (name),
    INDEX idx_name_ar (name_ar)
);

-- جدول الصور (Images)
CREATE TABLE images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VA<PERSON>HA<PERSON>(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    title VARCHAR(255),
    title_ar VARCHAR(255),
    description TEXT,
    description_ar TEXT,
    alt_text VARCHAR(255),
    alt_text_ar VARCHAR(255),
    file_size INT NOT NULL,
    width INT NOT NULL,
    height INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    thumbnail_path VARCHAR(500),
    medium_path VARCHAR(500),
    category_id INT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    views_count INT DEFAULT 0,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_category (category_id),
    INDEX idx_filename (filename),
    INDEX idx_upload_date (upload_date),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
);

-- جدول بيانات EXIF
CREATE TABLE image_exif (
    id INT AUTO_INCREMENT PRIMARY KEY,
    image_id INT NOT NULL,
    camera_make VARCHAR(100),
    camera_model VARCHAR(100),
    lens_model VARCHAR(100),
    focal_length VARCHAR(50),
    aperture VARCHAR(50),
    shutter_speed VARCHAR(50),
    iso VARCHAR(50),
    flash VARCHAR(100),
    date_taken DATETIME,
    gps_latitude DECIMAL(10, 8),
    gps_longitude DECIMAL(11, 8),
    orientation INT,
    color_space VARCHAR(50),
    white_balance VARCHAR(50),
    exposure_mode VARCHAR(50),
    metering_mode VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
    INDEX idx_image_id (image_id),
    INDEX idx_date_taken (date_taken)
);

-- جدول العلامات (Tags)
CREATE TABLE tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    name_ar VARCHAR(100),
    slug VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_slug (slug)
);

-- جدول ربط الصور بالعلامات
CREATE TABLE image_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    image_id INT NOT NULL,
    tag_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    UNIQUE KEY unique_image_tag (image_id, tag_id),
    INDEX idx_image_id (image_id),
    INDEX idx_tag_id (tag_id)
);

-- إدراج بعض الأصناف الافتراضية
INSERT INTO categories (name, name_ar, description, description_ar, slug) VALUES
('Nature', 'طبيعة', 'Beautiful nature photography', 'صور طبيعية جميلة', 'nature'),
('Portrait', 'بورتريه', 'Portrait photography', 'تصوير الأشخاص', 'portrait'),
('Landscape', 'مناظر طبيعية', 'Landscape photography', 'تصوير المناظر الطبيعية', 'landscape'),
('Architecture', 'عمارة', 'Architectural photography', 'التصوير المعماري', 'architecture'),
('Street', 'شارع', 'Street photography', 'تصوير الشارع', 'street'),
('Abstract', 'تجريدي', 'Abstract photography', 'التصوير التجريدي', 'abstract'),
('Wildlife', 'حياة برية', 'Wildlife photography', 'تصوير الحياة البرية', 'wildlife'),
('Events', 'فعاليات', 'Event photography', 'تصوير الفعاليات', 'events');

-- إدراج بعض العلامات الافتراضية
INSERT INTO tags (name, name_ar, slug) VALUES
('Sunset', 'غروب الشمس', 'sunset'),
('Black and White', 'أبيض وأسود', 'black-and-white'),
('Vintage', 'كلاسيكي', 'vintage'),
('Modern', 'حديث', 'modern'),
('Colorful', 'ملون', 'colorful'),
('Minimalist', 'بسيط', 'minimalist'),
('Urban', 'حضري', 'urban'),
('Rural', 'ريفي', 'rural');

-- إنشاء مستخدم إداري افتراضي (اختياري)
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email)
);

-- إدراج مستخدم إداري افتراضي (كلمة المرور: admin123)
INSERT INTO admin_users (username, email, password_hash, full_name) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام');
