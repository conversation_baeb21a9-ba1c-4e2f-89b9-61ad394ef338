<?php
/**
 * صفحة إدارة الأصناف
 * Categories Management Page
 */

require_once '../init.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$currentUser = $auth->getCurrentUser();
$categoryManager = new CategoryManager();

$message = '';
$messageType = '';

// معالجة إضافة صنف جديد
if (isset($_POST['add_category'])) {
    $data = [
        'name' => trim($_POST['name']),
        'name_ar' => trim($_POST['name_ar']),
        'description' => trim($_POST['description']),
        'description_ar' => trim($_POST['description_ar'])
    ];
    
    if (empty($data['name']) || empty($data['name_ar'])) {
        $message = __('name_required_both_languages');
        $messageType = 'danger';
    } elseif ($categoryManager->categoryExists($data['name'])) {
        $message = __('category_already_exists');
        $messageType = 'warning';
    } else {
        $categoryId = $categoryManager->addCategory($data);
        if ($categoryId) {
            $message = __('category_added_successfully');
            $messageType = 'success';
        } else {
            $message = __('failed_to_add_category');
            $messageType = 'danger';
        }
    }
}

// معالجة تحديث صنف
if (isset($_POST['update_category'])) {
    $categoryId = (int)$_POST['category_id'];
    $data = [
        'name' => trim($_POST['name']),
        'name_ar' => trim($_POST['name_ar']),
        'description' => trim($_POST['description']),
        'description_ar' => trim($_POST['description_ar'])
    ];
    
    if (empty($data['name']) || empty($data['name_ar'])) {
        $message = __('name_required_both_languages');
        $messageType = 'danger';
    } else {
        $result = $categoryManager->updateCategory($categoryId, $data);
        if ($result) {
            $message = __('category_updated_successfully');
            $messageType = 'success';
        } else {
            $message = __('failed_to_update_category');
            $messageType = 'danger';
        }
    }
}

// معالجة حذف صنف
if (isset($_POST['delete_category'])) {
    $categoryId = (int)$_POST['category_id'];
    $result = $categoryManager->deleteCategory($categoryId);
    
    if ($result) {
        $message = __('category_deleted_successfully');
        $messageType = 'success';
    } else {
        $message = __('failed_to_delete_category');
        $messageType = 'danger';
    }
}

// الحصول على الأصناف مع عدد الصور
$categories = $categoryManager->getCategoriesWithImageCount();

// الحصول على إحصائيات الأصناف
$categoryStats = $categoryManager->getCategoryStats();

$pageTitle = __('manage_categories') . META_TITLE_SEPARATOR . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="<?php echo $currentLang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
    <?php if ($isRTL): ?>
    <link href="../assets/css/rtl.css" rel="stylesheet">
    <link href="assets/css/admin-rtl.css" rel="stylesheet">
    <?php endif; ?>
</head>
<body class="admin-body">
    <!-- Navigation -->
    <?php include 'includes/nav.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1"><?php echo __('manage_categories'); ?></h1>
                <p class="text-muted mb-0">
                    <?php echo $categoryStats['total_categories']; ?> <?php echo __('total_categories'); ?> • 
                    <?php echo $categoryStats['categories_with_images']; ?> <?php echo __('with_images'); ?>
                </p>
            </div>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus me-1"></i><?php echo __('add_category'); ?>
                </button>
            </div>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Categories Grid -->
        <?php if (!empty($categories)): ?>
        <div class="row g-4">
            <?php foreach ($categories as $category): ?>
            <div class="col-xl-4 col-lg-6">
                <div class="card category-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-folder me-2 text-primary"></i>
                            <?php echo htmlspecialchars($currentLang === 'ar' ? $category['name_ar'] : $category['name']); ?>
                        </h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                    type="button" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="../index.php?category=<?php echo $category['id']; ?>" target="_blank">
                                    <i class="fas fa-eye me-2"></i><?php echo __('view_images'); ?>
                                </a></li>
                                <li><a class="dropdown-item edit-category-btn" 
                                       href="#"
                                       data-category-id="<?php echo $category['id']; ?>"
                                       data-name="<?php echo htmlspecialchars($category['name']); ?>"
                                       data-name-ar="<?php echo htmlspecialchars($category['name_ar']); ?>"
                                       data-description="<?php echo htmlspecialchars($category['description']); ?>"
                                       data-description-ar="<?php echo htmlspecialchars($category['description_ar']); ?>">
                                    <i class="fas fa-edit me-2"></i><?php echo __('edit'); ?>
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger delete-category-btn" 
                                       href="#"
                                       data-category-id="<?php echo $category['id']; ?>"
                                       data-category-name="<?php echo htmlspecialchars($currentLang === 'ar' ? $category['name_ar'] : $category['name']); ?>"
                                       data-image-count="<?php echo $category['image_count']; ?>">
                                    <i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($category['description'] || $category['description_ar']): ?>
                        <p class="card-text text-muted">
                            <?php echo nl2br(htmlspecialchars($currentLang === 'ar' ? $category['description_ar'] : $category['description'])); ?>
                        </p>
                        <?php endif; ?>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-primary">
                                    <i class="fas fa-images me-1"></i>
                                    <?php echo number_format($category['image_count']); ?> <?php echo __('images'); ?>
                                </span>
                            </div>
                            <small class="text-muted">
                                <?php echo __('created'); ?>: <?php echo formatDate($category['created_at'], 'M j, Y'); ?>
                            </small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex justify-content-between">
                            <a href="../index.php?category=<?php echo $category['id']; ?>" 
                               class="btn btn-sm btn-outline-primary" 
                               target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i><?php echo __('view'); ?>
                            </a>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary edit-category-btn"
                                        data-category-id="<?php echo $category['id']; ?>"
                                        data-name="<?php echo htmlspecialchars($category['name']); ?>"
                                        data-name-ar="<?php echo htmlspecialchars($category['name_ar']); ?>"
                                        data-description="<?php echo htmlspecialchars($category['description']); ?>"
                                        data-description-ar="<?php echo htmlspecialchars($category['description_ar']); ?>">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger delete-category-btn"
                                        data-category-id="<?php echo $category['id']; ?>"
                                        data-category-name="<?php echo htmlspecialchars($currentLang === 'ar' ? $category['name_ar'] : $category['name']); ?>"
                                        data-image-count="<?php echo $category['image_count']; ?>">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <?php else: ?>
        <!-- No Categories -->
        <div class="text-center py-5">
            <i class="fas fa-folder fa-3x text-muted mb-3"></i>
            <h4><?php echo __('no_categories_yet'); ?></h4>
            <p class="text-muted"><?php echo __('create_first_category'); ?></p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="fas fa-plus me-1"></i><?php echo __('add_first_category'); ?>
            </button>
        </div>
        <?php endif; ?>
    </div>

    <!-- Add Category Modal -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_new_category'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="add_name" class="form-label"><?php echo __('name'); ?> (<?php echo __('english'); ?>) *</label>
                            <input type="text" class="form-control" id="add_name" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="add_name_ar" class="form-label"><?php echo __('name'); ?> (<?php echo __('arabic'); ?>) *</label>
                            <input type="text" class="form-control" id="add_name_ar" name="name_ar" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="add_description" class="form-label"><?php echo __('description'); ?> (<?php echo __('english'); ?>)</label>
                            <textarea class="form-control" id="add_description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="add_description_ar" class="form-label"><?php echo __('description'); ?> (<?php echo __('arabic'); ?>)</label>
                            <textarea class="form-control" id="add_description_ar" name="description_ar" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i><?php echo __('cancel'); ?>
                        </button>
                        <button type="submit" name="add_category" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i><?php echo __('add_category'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div class="modal fade" id="editCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i><?php echo __('edit_category'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="category_id" id="edit_category_id">
                        
                        <div class="mb-3">
                            <label for="edit_name" class="form-label"><?php echo __('name'); ?> (<?php echo __('english'); ?>) *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_name_ar" class="form-label"><?php echo __('name'); ?> (<?php echo __('arabic'); ?>) *</label>
                            <input type="text" class="form-control" id="edit_name_ar" name="name_ar" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_description" class="form-label"><?php echo __('description'); ?> (<?php echo __('english'); ?>)</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_description_ar" class="form-label"><?php echo __('description'); ?> (<?php echo __('arabic'); ?>)</label>
                            <textarea class="form-control" id="edit_description_ar" name="description_ar" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i><?php echo __('cancel'); ?>
                        </button>
                        <button type="submit" name="update_category" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i><?php echo __('update_category'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Category Modal -->
    <div class="modal fade" id="deleteCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        <?php echo __('confirm_delete'); ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p><?php echo __('delete_category_confirmation'); ?></p>
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo __('category_delete_warning'); ?>
                    </div>
                    <div id="category-to-delete"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i><?php echo __('cancel'); ?>
                    </button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="category_id" id="delete_category_id">
                        <button type="submit" name="delete_category" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i><?php echo __('yes_delete'); ?>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/gallery.js"></script>
    <script src="assets/js/admin.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Edit category buttons
            document.querySelectorAll('.edit-category-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const categoryId = this.dataset.categoryId;
                    const name = this.dataset.name;
                    const nameAr = this.dataset.nameAr;
                    const description = this.dataset.description;
                    const descriptionAr = this.dataset.descriptionAr;
                    
                    document.getElementById('edit_category_id').value = categoryId;
                    document.getElementById('edit_name').value = name;
                    document.getElementById('edit_name_ar').value = nameAr;
                    document.getElementById('edit_description').value = description;
                    document.getElementById('edit_description_ar').value = descriptionAr;
                    
                    const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
                    modal.show();
                });
            });
            
            // Delete category buttons
            document.querySelectorAll('.delete-category-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const categoryId = this.dataset.categoryId;
                    const categoryName = this.dataset.categoryName;
                    const imageCount = parseInt(this.dataset.imageCount);
                    
                    document.getElementById('delete_category_id').value = categoryId;
                    
                    let deleteInfo = `<strong><?php echo __('category'); ?>:</strong> ${categoryName}`;
                    if (imageCount > 0) {
                        deleteInfo += `<br><strong><?php echo __('images_count'); ?>:</strong> ${imageCount}`;
                    }
                    
                    document.getElementById('category-to-delete').innerHTML = deleteInfo;
                    
                    const modal = new bootstrap.Modal(document.getElementById('deleteCategoryModal'));
                    modal.show();
                });
            });
            
            // Form validation
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const nameInput = this.querySelector('input[name="name"]');
                    const nameArInput = this.querySelector('input[name="name_ar"]');
                    
                    if (nameInput && nameArInput) {
                        if (!nameInput.value.trim() || !nameArInput.value.trim()) {
                            e.preventDefault();
                            alert('<?php echo __('name_required_both_languages'); ?>');
                            return false;
                        }
                    }
                });
            });
            
            // Auto-generate Arabic name (optional helper)
            document.getElementById('add_name').addEventListener('input', function() {
                const nameArInput = document.getElementById('add_name_ar');
                if (!nameArInput.value) {
                    // يمكن إضافة منطق ترجمة تلقائية هنا
                }
            });
        });
    </script>
</body>
</html>
