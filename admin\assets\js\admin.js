/**
 * ملف JavaScript الرئيسي للوحة الإدارة
 * Main Admin Panel JavaScript
 */

(function() {
    'use strict';

    // متغيرات عامة
    let currentLanguage = document.documentElement.lang || 'ar';
    let isRTL = document.documentElement.dir === 'rtl';
    
    // النصوص متعددة اللغات
    const translations = {
        ar: {
            loading: 'جاري التحميل...',
            saving: 'جاري الحفظ...',
            deleting: 'جاري الحذف...',
            processing: 'جاري المعالجة...',
            success: 'تم بنجاح',
            error: 'حدث خطأ',
            warning: 'تحذير',
            confirm: 'تأكيد',
            cancel: 'إلغاء',
            close: 'إغلاق',
            save: 'حفظ',
            delete: 'حذف',
            edit: 'تعديل',
            view: 'عرض',
            selectAll: 'تحديد الكل',
            deselectAll: 'إلغاء تحديد الكل',
            noItemsSelected: 'لم يتم تحديد أي عناصر',
            confirmBulkDelete: 'هل أنت متأكد من حذف العناصر المحددة؟',
            operationCompleted: 'تمت العملية بنجاح',
            operationFailed: 'فشلت العملية',
            networkError: 'خطأ في الشبكة',
            invalidResponse: 'استجابة غير صحيحة من الخادم'
        },
        en: {
            loading: 'Loading...',
            saving: 'Saving...',
            deleting: 'Deleting...',
            processing: 'Processing...',
            success: 'Success',
            error: 'Error',
            warning: 'Warning',
            confirm: 'Confirm',
            cancel: 'Cancel',
            close: 'Close',
            save: 'Save',
            delete: 'Delete',
            edit: 'Edit',
            view: 'View',
            selectAll: 'Select All',
            deselectAll: 'Deselect All',
            noItemsSelected: 'No items selected',
            confirmBulkDelete: 'Are you sure you want to delete selected items?',
            operationCompleted: 'Operation completed successfully',
            operationFailed: 'Operation failed',
            networkError: 'Network error',
            invalidResponse: 'Invalid response from server'
        }
    };

    // دالة للحصول على النص المترجم
    function __(key) {
        return translations[currentLanguage] && translations[currentLanguage][key] 
            ? translations[currentLanguage][key] 
            : key;
    }

    // تهيئة التطبيق عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        initializeAdminFeatures();
        initializeFormValidation();
        initializeBulkActions();
        initializeTooltips();
        initializeConfirmations();
        initializeAutoSave();
        initializeKeyboardShortcuts();
    });

    // تهيئة ميزات الإدارة
    function initializeAdminFeatures() {
        // تحسين الجداول
        enhanceTables();
        
        // تحسين النماذج
        enhanceForms();
        
        // تحسين الأزرار
        enhanceButtons();
        
        // إضافة مؤشرات التحميل
        addLoadingIndicators();
    }

    // تحسين الجداول
    function enhanceTables() {
        const tables = document.querySelectorAll('.table');
        
        tables.forEach(table => {
            // إضافة تأثيرات الصفوف
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                row.style.animationDelay = (index * 0.05) + 's';
                row.classList.add('fade-in');
            });
            
            // تحسين الفرز
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    sortTable(table, this.dataset.sort);
                });
            });
        });
    }

    // تحسين النماذج
    function enhanceForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            // إضافة تحقق في الوقت الفعلي
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateField(this);
                });
                
                input.addEventListener('input', function() {
                    clearFieldError(this);
                });
            });
            
            // معالجة الإرسال
            form.addEventListener('submit', function(e) {
                if (!validateForm(this)) {
                    e.preventDefault();
                    return false;
                }
                
                showFormLoading(this);
            });
        });
    }

    // تحسين الأزرار
    function enhanceButtons() {
        const buttons = document.querySelectorAll('.btn');
        
        buttons.forEach(button => {
            // إضافة تأثيرات الضغط
            button.addEventListener('click', function() {
                if (!this.disabled) {
                    this.classList.add('btn-clicked');
                    setTimeout(() => {
                        this.classList.remove('btn-clicked');
                    }, 150);
                }
            });
        });
    }

    // إضافة مؤشرات التحميل
    function addLoadingIndicators() {
        const loadingButtons = document.querySelectorAll('[data-loading]');
        
        loadingButtons.forEach(button => {
            button.addEventListener('click', function() {
                showButtonLoading(this);
            });
        });
    }

    // عرض حالة تحميل الزر
    function showButtonLoading(button) {
        const originalText = button.innerHTML;
        const loadingText = button.dataset.loading || __('loading');
        
        button.innerHTML = `<i class="fas fa-spinner fa-spin me-1"></i>${loadingText}`;
        button.disabled = true;
        
        // إعادة تعيين الزر بعد 5 ثوان (في حالة عدم إعادة التحميل)
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 5000);
    }

    // عرض حالة تحميل النموذج
    function showFormLoading(form) {
        const submitButton = form.querySelector('button[type="submit"]');
        if (submitButton) {
            showButtonLoading(submitButton);
        }
        
        // إضافة overlay للنموذج
        const overlay = document.createElement('div');
        overlay.className = 'form-loading-overlay';
        overlay.innerHTML = `
            <div class="loading-spinner"></div>
            <div class="mt-2">${__('processing')}</div>
        `;
        
        form.style.position = 'relative';
        form.appendChild(overlay);
    }

    // تهيئة التحقق من النماذج
    function initializeFormValidation() {
        // قواعد التحقق المخصصة
        const validationRules = {
            required: function(value) {
                return value.trim() !== '';
            },
            email: function(value) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
            },
            minLength: function(value, min) {
                return value.length >= min;
            },
            maxLength: function(value, max) {
                return value.length <= max;
            },
            numeric: function(value) {
                return /^\d+$/.test(value);
            },
            url: function(value) {
                try {
                    new URL(value);
                    return true;
                } catch {
                    return false;
                }
            }
        };
        
        window.AdminValidation = validationRules;
    }

    // التحقق من حقل واحد
    function validateField(field) {
        const rules = field.dataset.validate ? field.dataset.validate.split('|') : [];
        const errors = [];
        
        rules.forEach(rule => {
            const [ruleName, ruleValue] = rule.split(':');
            
            if (window.AdminValidation[ruleName]) {
                const isValid = ruleValue 
                    ? window.AdminValidation[ruleName](field.value, ruleValue)
                    : window.AdminValidation[ruleName](field.value);
                
                if (!isValid) {
                    errors.push(getValidationMessage(ruleName, ruleValue));
                }
            }
        });
        
        if (errors.length > 0) {
            showFieldError(field, errors[0]);
            return false;
        } else {
            clearFieldError(field);
            return true;
        }
    }

    // التحقق من النموذج كاملاً
    function validateForm(form) {
        const fields = form.querySelectorAll('input[data-validate], textarea[data-validate], select[data-validate]');
        let isValid = true;
        
        fields.forEach(field => {
            if (!validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    // عرض خطأ الحقل
    function showFieldError(field, message) {
        clearFieldError(field);
        
        field.classList.add('is-invalid');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    }

    // مسح خطأ الحقل
    function clearFieldError(field) {
        field.classList.remove('is-invalid');
        
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    // الحصول على رسالة التحقق
    function getValidationMessage(rule, value) {
        const messages = {
            ar: {
                required: 'هذا الحقل مطلوب',
                email: 'يرجى إدخال بريد إلكتروني صحيح',
                minLength: `يجب أن يكون النص ${value} أحرف على الأقل`,
                maxLength: `يجب أن لا يتجاوز النص ${value} حرف`,
                numeric: 'يرجى إدخال أرقام فقط',
                url: 'يرجى إدخال رابط صحيح'
            },
            en: {
                required: 'This field is required',
                email: 'Please enter a valid email address',
                minLength: `Text must be at least ${value} characters`,
                maxLength: `Text must not exceed ${value} characters`,
                numeric: 'Please enter numbers only',
                url: 'Please enter a valid URL'
            }
        };
        
        return messages[currentLanguage] && messages[currentLanguage][rule] 
            ? messages[currentLanguage][rule] 
            : `Validation error: ${rule}`;
    }

    // تهيئة الإجراءات المجمعة
    function initializeBulkActions() {
        const selectAllCheckbox = document.getElementById('select-all');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox, .image-checkbox, .category-checkbox, .tag-checkbox');
        const bulkActionButtons = document.querySelectorAll('[data-bulk-action]');
        
        if (selectAllCheckbox && itemCheckboxes.length > 0) {
            selectAllCheckbox.addEventListener('change', function() {
                itemCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActionButtons();
            });
            
            itemCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectAllState();
                    updateBulkActionButtons();
                });
            });
        }
        
        function updateSelectAllState() {
            const checkedCount = document.querySelectorAll('.item-checkbox:checked, .image-checkbox:checked, .category-checkbox:checked, .tag-checkbox:checked').length;
            const totalCount = itemCheckboxes.length;
            
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checkedCount === totalCount;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
            }
        }
        
        function updateBulkActionButtons() {
            const checkedCount = document.querySelectorAll('.item-checkbox:checked, .image-checkbox:checked, .category-checkbox:checked, .tag-checkbox:checked').length;
            
            bulkActionButtons.forEach(button => {
                button.disabled = checkedCount === 0;
                
                // تحديث نص الزر
                const originalText = button.dataset.originalText || button.textContent;
                if (!button.dataset.originalText) {
                    button.dataset.originalText = originalText;
                }
                
                if (checkedCount > 0) {
                    button.innerHTML = originalText.replace(/\(\d+\)/, `(${checkedCount})`);
                    if (!originalText.includes('(')) {
                        button.innerHTML += ` (${checkedCount})`;
                    }
                } else {
                    button.innerHTML = originalText.replace(/\s*\(\d+\)/, '');
                }
            });
        }
    }

    // تهيئة التلميحات
    function initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // إضافة تلميحات تلقائية للأزرار
        const buttons = document.querySelectorAll('.btn[title]');
        buttons.forEach(button => {
            if (!button.hasAttribute('data-bs-toggle')) {
                button.setAttribute('data-bs-toggle', 'tooltip');
                new bootstrap.Tooltip(button);
            }
        });
    }

    // تهيئة التأكيدات
    function initializeConfirmations() {
        const confirmButtons = document.querySelectorAll('[data-confirm]');
        
        confirmButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                const message = this.dataset.confirm || __('confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                    return false;
                }
            });
        });
    }

    // تهيئة الحفظ التلقائي
    function initializeAutoSave() {
        const autoSaveForms = document.querySelectorAll('[data-auto-save]');
        
        autoSaveForms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            let saveTimeout;
            
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(() => {
                        autoSaveForm(form);
                    }, 2000); // حفظ بعد ثانيتين من التوقف عن الكتابة
                });
            });
        });
    }

    // الحفظ التلقائي للنموذج
    function autoSaveForm(form) {
        const formData = new FormData(form);
        formData.append('auto_save', '1');
        
        fetch(form.action || window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAutoSaveIndicator(true);
            } else {
                showAutoSaveIndicator(false);
            }
        })
        .catch(error => {
            console.error('Auto-save error:', error);
            showAutoSaveIndicator(false);
        });
    }

    // عرض مؤشر الحفظ التلقائي
    function showAutoSaveIndicator(success) {
        const indicator = document.getElementById('auto-save-indicator') || createAutoSaveIndicator();
        
        indicator.className = success ? 'auto-save-success' : 'auto-save-error';
        indicator.textContent = success ? __('saved') : __('save_failed');
        indicator.style.display = 'block';
        
        setTimeout(() => {
            indicator.style.display = 'none';
        }, 2000);
    }

    // إنشاء مؤشر الحفظ التلقائي
    function createAutoSaveIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'auto-save-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 9999;
            display: none;
        `;
        
        document.body.appendChild(indicator);
        return indicator;
    }

    // تهيئة اختصارات لوحة المفاتيح
    function initializeKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + S للحفظ
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                const activeForm = document.querySelector('form:focus-within');
                if (activeForm) {
                    activeForm.submit();
                }
            }
            
            // Ctrl/Cmd + N لإضافة جديد
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                const addButton = document.querySelector('[data-bs-target*="add"], [data-bs-target*="Add"]');
                if (addButton) {
                    addButton.click();
                }
            }
            
            // ESC لإغلاق النوافذ المنبثقة
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal.show');
                modals.forEach(modal => {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) bsModal.hide();
                });
            }
        });
    }

    // عرض الإشعارات
    function showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show admin-notification`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            max-width: 500px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${getNotificationIcon(type)} me-2"></i>
                <div>${message}</div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // إزالة الإشعار تلقائياً
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            document.body.removeChild(notification);
                        }
                    }, 150);
                }
            }, duration);
        }
        
        return notification;
    }

    // الحصول على أيقونة الإشعار
    function getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            danger: 'exclamation-triangle',
            warning: 'exclamation-triangle',
            info: 'info-circle',
            primary: 'info-circle'
        };
        
        return icons[type] || 'info-circle';
    }

    // طلبات AJAX
    function makeAjaxRequest(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const finalOptions = Object.assign(defaultOptions, options);
        
        return fetch(url, finalOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('AJAX Error:', error);
                showNotification(__('networkError'), 'danger');
                throw error;
            });
    }

    // تصدير الدوال للاستخدام العام
    window.AdminApp = {
        showNotification: showNotification,
        showButtonLoading: showButtonLoading,
        makeAjaxRequest: makeAjaxRequest,
        validateField: validateField,
        validateForm: validateForm,
        __: __
    };

})();
