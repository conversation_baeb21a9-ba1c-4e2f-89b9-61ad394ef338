<?php
/**
 * صفحة عرض الصورة المفردة
 * Single Image View Page
 */

require_once 'init.php';

// الحصول على معرف الصورة
$imageId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$imageId) {
    header('Location: index.php');
    exit;
}

// إنشاء كائنات الإدارة
$imageManager = new ImageManager();
$categoryManager = new CategoryManager();
$tagManager = new TagManager();

// الحصول على الصورة
$image = $imageManager->getImage($imageId);

if (!$image) {
    header('Location: index.php');
    exit;
}

// زيادة عدد المشاهدات
$imageManager->incrementViews($imageId);

// الحصول على علامات الصورة
$imageTags = $imageManager->getImageTags($imageId);

// الحصول على بيانات EXIF
$exifData = [];
$exifQuery = "SELECT * FROM image_exif WHERE image_id = ?";
$exifResult = $db->selectOne($exifQuery, [$imageId]);
if ($exifResult) {
    $exifData = $exifResult;
}

// الحصول على صور مشابهة (من نفس الصنف)
$relatedImages = [];
if ($image['category_id']) {
    $relatedFilters = ['category_id' => $image['category_id']];
    $allRelated = $imageManager->getImages($relatedFilters, 1, 8);
    // إزالة الصورة الحالية من النتائج
    $relatedImages = array_filter($allRelated, function($img) use ($imageId) {
        return $img['id'] != $imageId;
    });
    $relatedImages = array_slice($relatedImages, 0, 6);
}

// إعداد العنوان والوصف
$pageTitle = ($currentLang === 'ar' ? $image['title_ar'] : $image['title']) . META_TITLE_SEPARATOR . SITE_NAME;
$pageDescription = $currentLang === 'ar' ? $image['description_ar'] : $image['description'];
if (empty($pageDescription)) {
    $pageDescription = ($currentLang === 'ar' ? $image['title_ar'] : $image['title']) . ' - ' . SITE_DESCRIPTION;
}

// معلومات إضافية
$imageUrl = UPLOADS_URL . $image['file_path'];
$thumbnailUrl = THUMBNAILS_URL . basename($image['thumbnail_path']);
$mediumUrl = MEDIUM_URL . basename($image['medium_path']);
?>
<!DOCTYPE html>
<html lang="<?php echo $currentLang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($currentLang === 'ar' ? $image['title_ar'] : $image['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta property="og:image" content="<?php echo $mediumUrl; ?>">
    <meta property="og:url" content="<?php echo createUrl('image.php', ['id' => $imageId]); ?>">
    <meta property="og:type" content="article">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($currentLang === 'ar' ? $image['title_ar'] : $image['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="twitter:image" content="<?php echo $mediumUrl; ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Lightbox CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <?php if ($isRTL): ?>
    <link href="assets/css/rtl.css" rel="stylesheet">
    <?php endif; ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-camera me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i><?php echo __('home'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-images me-1"></i><?php echo __('gallery'); ?>
                        </a>
                    </li>
                    <?php if ($image['category_id']): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?category=<?php echo $image['category_id']; ?>">
                            <i class="fas fa-folder me-1"></i>
                            <?php echo htmlspecialchars($currentLang === 'ar' ? $image['category_name_ar'] : $image['category_name']); ?>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                
                <!-- Language Switcher -->
                <div class="dropdown">
                    <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        <?php echo strtoupper($currentLang); ?>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?id=<?php echo $imageId; ?>&lang=ar">العربية</a></li>
                        <li><a class="dropdown-item" href="?id=<?php echo $imageId; ?>&lang=en">English</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php"><?php echo __('home'); ?></a></li>
                <li class="breadcrumb-item"><a href="index.php"><?php echo __('gallery'); ?></a></li>
                <?php if ($image['category_id']): ?>
                <li class="breadcrumb-item">
                    <a href="index.php?category=<?php echo $image['category_id']; ?>">
                        <?php echo htmlspecialchars($currentLang === 'ar' ? $image['category_name_ar'] : $image['category_name']); ?>
                    </a>
                </li>
                <?php endif; ?>
                <li class="breadcrumb-item active">
                    <?php echo htmlspecialchars($currentLang === 'ar' ? $image['title_ar'] : $image['title']); ?>
                </li>
            </ol>
        </nav>

        <div class="row">
            <!-- Image Display -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body p-0">
                        <div class="position-relative">
                            <img src="<?php echo $imageUrl; ?>" 
                                 class="img-fluid w-100" 
                                 alt="<?php echo htmlspecialchars($currentLang === 'ar' ? $image['alt_text_ar'] : $image['alt_text']); ?>"
                                 id="main-image">
                            
                            <!-- Image Actions Overlay -->
                            <div class="image-actions-overlay">
                                <div class="btn-group" role="group">
                                    <a href="<?php echo $imageUrl; ?>" 
                                       data-lightbox="single" 
                                       class="btn btn-light btn-sm" 
                                       title="<?php echo __('fullscreen'); ?>">
                                        <i class="fas fa-expand"></i>
                                    </a>
                                    <a href="<?php echo $imageUrl; ?>" 
                                       download 
                                       class="btn btn-light btn-sm" 
                                       title="<?php echo __('download'); ?>">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <button class="btn btn-light btn-sm" 
                                            onclick="shareImage()" 
                                            title="<?php echo __('share'); ?>">
                                        <i class="fas fa-share"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Image Information -->
                <div class="card mt-3">
                    <div class="card-body">
                        <h1 class="h4 mb-3">
                            <?php echo htmlspecialchars($currentLang === 'ar' ? $image['title_ar'] : $image['title']); ?>
                        </h1>
                        
                        <?php if ($image['description'] || $image['description_ar']): ?>
                        <p class="text-muted">
                            <?php echo nl2br(htmlspecialchars($currentLang === 'ar' ? $image['description_ar'] : $image['description'])); ?>
                        </p>
                        <?php endif; ?>

                        <!-- Image Meta -->
                        <div class="row g-3 mt-2">
                            <div class="col-sm-6">
                                <small class="text-muted d-block"><?php echo __('upload_date'); ?></small>
                                <span><?php echo formatDate($image['upload_date']); ?></span>
                            </div>
                            <div class="col-sm-6">
                                <small class="text-muted d-block"><?php echo __('views'); ?></small>
                                <span><i class="fas fa-eye me-1"></i><?php echo number_format($image['views_count']); ?></span>
                            </div>
                            <div class="col-sm-6">
                                <small class="text-muted d-block"><?php echo __('file_size'); ?></small>
                                <span><?php echo formatFileSize($image['file_size']); ?></span>
                            </div>
                            <div class="col-sm-6">
                                <small class="text-muted d-block"><?php echo __('dimensions'); ?></small>
                                <span><?php echo $image['width']; ?> × <?php echo $image['height']; ?> px</span>
                            </div>
                        </div>

                        <!-- Tags -->
                        <?php if (!empty($imageTags)): ?>
                        <div class="mt-3">
                            <small class="text-muted d-block mb-2"><?php echo __('tags'); ?></small>
                            <div class="tag-list">
                                <?php foreach ($imageTags as $tag): ?>
                                <a href="index.php?tag=<?php echo $tag['id']; ?>" 
                                   class="badge bg-light text-dark me-1 mb-1 text-decoration-none">
                                    <i class="fas fa-tag me-1"></i>
                                    <?php echo htmlspecialchars($currentLang === 'ar' ? $tag['name_ar'] : $tag['name']); ?>
                                </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- EXIF Data -->
                <?php if (!empty($exifData)): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-camera me-2"></i><?php echo __('camera_info'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="exif-data">
                            <?php if ($exifData['camera_make']): ?>
                            <div class="mb-2">
                                <small class="text-muted d-block"><?php echo __('camera_make'); ?></small>
                                <span><?php echo htmlspecialchars($exifData['camera_make']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($exifData['camera_model']): ?>
                            <div class="mb-2">
                                <small class="text-muted d-block"><?php echo __('camera_model'); ?></small>
                                <span><?php echo htmlspecialchars($exifData['camera_model']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($exifData['lens_model']): ?>
                            <div class="mb-2">
                                <small class="text-muted d-block"><?php echo __('lens_model'); ?></small>
                                <span><?php echo htmlspecialchars($exifData['lens_model']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($exifData['focal_length']): ?>
                            <div class="mb-2">
                                <small class="text-muted d-block"><?php echo __('focal_length'); ?></small>
                                <span><?php echo htmlspecialchars($exifData['focal_length']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($exifData['aperture']): ?>
                            <div class="mb-2">
                                <small class="text-muted d-block"><?php echo __('aperture'); ?></small>
                                <span><?php echo htmlspecialchars($exifData['aperture']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($exifData['shutter_speed']): ?>
                            <div class="mb-2">
                                <small class="text-muted d-block"><?php echo __('shutter_speed'); ?></small>
                                <span><?php echo htmlspecialchars($exifData['shutter_speed']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($exifData['iso']): ?>
                            <div class="mb-2">
                                <small class="text-muted d-block"><?php echo __('iso'); ?></small>
                                <span>ISO <?php echo htmlspecialchars($exifData['iso']); ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($exifData['date_taken']): ?>
                            <div class="mb-2">
                                <small class="text-muted d-block"><?php echo __('date_taken'); ?></small>
                                <span><?php echo formatDate($exifData['date_taken']); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Related Images -->
                <?php if (!empty($relatedImages)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-images me-2"></i><?php echo __('related_images'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <?php foreach ($relatedImages as $relatedImage): ?>
                            <div class="col-6">
                                <a href="image.php?id=<?php echo $relatedImage['id']; ?>">
                                    <img src="<?php echo THUMBNAILS_URL . basename($relatedImage['thumbnail_path']); ?>" 
                                         class="img-fluid rounded" 
                                         alt="<?php echo htmlspecialchars($currentLang === 'ar' ? $relatedImage['alt_text_ar'] : $relatedImage['alt_text']); ?>">
                                </a>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="text-center mt-3">
                            <a href="index.php?category=<?php echo $image['category_id']; ?>" 
                               class="btn btn-sm btn-outline-primary">
                                <?php echo __('view_all'); ?>
                            </a>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p><?php echo SITE_DESCRIPTION; ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. <?php echo __('all_rights_reserved'); ?></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
    <script>
        function shareImage() {
            if (navigator.share) {
                navigator.share({
                    title: '<?php echo addslashes($currentLang === 'ar' ? $image['title_ar'] : $image['title']); ?>',
                    text: '<?php echo addslashes($pageDescription); ?>',
                    url: window.location.href
                });
            } else {
                // Fallback: copy URL to clipboard
                navigator.clipboard.writeText(window.location.href).then(function() {
                    alert('<?php echo __('link_copied'); ?>');
                });
            }
        }
    </script>
</body>
</html>
