<?php
/**
 * خطوة 2: إعداد قاعدة البيانات
 * Step 2: Database Setup
 */
?>

<div class="text-center mb-4">
    <h3><i class="fas fa-database me-2"></i>إعداد قاعدة البيانات</h3>
    <p class="text-muted">أدخل معلومات الاتصال بقاعدة البيانات</p>
</div>

<form method="POST">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">معلومات قاعدة البيانات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_host" class="form-label">عنوان الخادم *</label>
                        <input type="text" 
                               class="form-control" 
                               id="db_host" 
                               name="db_host" 
                               value="<?php echo $_POST['db_host'] ?? 'localhost'; ?>"
                               placeholder="localhost"
                               required>
                        <div class="form-text">عادة ما يكون localhost</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_name" class="form-label">اسم قاعدة البيانات *</label>
                        <input type="text" 
                               class="form-control" 
                               id="db_name" 
                               name="db_name" 
                               value="<?php echo $_POST['db_name'] ?? 'photo_gallery'; ?>"
                               placeholder="photo_gallery"
                               required>
                        <div class="form-text">سيتم إنشاؤها إذا لم تكن موجودة</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_user" class="form-label">اسم المستخدم *</label>
                        <input type="text" 
                               class="form-control" 
                               id="db_user" 
                               name="db_user" 
                               value="<?php echo $_POST['db_user'] ?? ''; ?>"
                               placeholder="database_username"
                               required>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="db_pass" class="form-label">كلمة المرور</label>
                        <div class="input-group">
                            <input type="password" 
                                   class="form-control" 
                                   id="db_pass" 
                                   name="db_pass" 
                                   value="<?php echo $_POST['db_pass'] ?? ''; ?>"
                                   placeholder="database_password">
                            <button class="btn btn-outline-secondary" 
                                    type="button" 
                                    onclick="togglePassword('db_pass')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="form-text">اتركها فارغة إذا لم تكن هناك كلمة مرور</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title mb-0">إعدادات الموقع الأساسية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="site_name" class="form-label">اسم الموقع</label>
                        <input type="text" 
                               class="form-control" 
                               id="site_name" 
                               name="site_name" 
                               value="<?php echo $_POST['site_name'] ?? 'معرض الصور'; ?>"
                               placeholder="معرض الصور">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="default_language" class="form-label">اللغة الافتراضية</label>
                        <select class="form-select" id="default_language" name="default_language">
                            <option value="ar" <?php echo ($_POST['default_language'] ?? 'ar') === 'ar' ? 'selected' : ''; ?>>العربية</option>
                            <option value="en" <?php echo ($_POST['default_language'] ?? 'ar') === 'en' ? 'selected' : ''; ?>>English</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="site_description" class="form-label">وصف الموقع</label>
                <textarea class="form-control" 
                          id="site_description" 
                          name="site_description" 
                          rows="2"
                          placeholder="معرض صور احترافي متعدد اللغات"><?php echo $_POST['site_description'] ?? 'معرض صور احترافي متعدد اللغات'; ?></textarea>
            </div>
        </div>
    </div>
    
    <div class="alert alert-info mt-4">
        <h6><i class="fas fa-info-circle me-2"></i>ملاحظة مهمة</h6>
        <p class="mb-0">
            تأكد من أن مستخدم قاعدة البيانات لديه صلاحيات إنشاء قواعد البيانات والجداول.
            سيتم إنشاء قاعدة البيانات تلقائياً إذا لم تكن موجودة.
        </p>
    </div>
    
    <div class="d-flex justify-content-between mt-4">
        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
            <i class="fas fa-arrow-left me-2"></i>
            السابق
        </button>
        
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-database me-2"></i>
            اختبار الاتصال والمتابعة
        </button>
    </div>
</form>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// تحقق من الاتصال في الوقت الفعلي
document.getElementById('db_host').addEventListener('blur', testConnection);
document.getElementById('db_user').addEventListener('blur', testConnection);
document.getElementById('db_pass').addEventListener('blur', testConnection);

function testConnection() {
    const host = document.getElementById('db_host').value;
    const user = document.getElementById('db_user').value;
    const pass = document.getElementById('db_pass').value;
    
    if (host && user) {
        // يمكن إضافة AJAX لاختبار الاتصال هنا
        console.log('Testing connection...');
    }
}
</script>
