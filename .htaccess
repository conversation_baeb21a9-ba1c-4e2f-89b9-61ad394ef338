# ===================================
# ملف .htaccess لمعرض الصور
# Photo Gallery .htaccess File
# ===================================

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# ===================================
# الحماية الأمنية
# Security Protection
# ===================================

# منع الوصول للملفات الحساسة
<FilesMatch "\.(sql|log|md|txt|json|lock|yml|yaml|env)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# حماية ملفات PHP الحساسة
<FilesMatch "^(config|install|init|\.env)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع عرض محتويات المجلدات
Options -Indexes

# حماية من هجمات الحقن
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} ^.*(\[|\]|\(|\)|<|>|ê|"|;|\?|\*|=$).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*("|'|<|>|\|{2}).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(%0|%A|%B|%C|%D|%E|%F|127\.0).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(globals|encode|localhost|loopback).* [NC,OR]
    RewriteCond %{QUERY_STRING} ^.*(request|select|insert|union|declare).* [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# ===================================
# إعدادات الضغط
# Compression Settings
# ===================================

<IfModule mod_deflate.c>
    # ضغط ملفات النصوص
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# ===================================
# إعدادات التخزين المؤقت
# Caching Settings
# ===================================

<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # ملفات أخرى
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# إعدادات Cache-Control
<IfModule mod_headers.c>
    # الصور
    <FilesMatch "\.(jpg|jpeg|png|gif|webp|svg|ico)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # CSS و JavaScript
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # الخطوط
    <FilesMatch "\.(woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
    
    # ملفات HTML
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
</IfModule>

# ===================================
# إعادة كتابة الروابط الودودة
# SEO Friendly URLs
# ===================================

<IfModule mod_rewrite.c>
    # إعادة توجيه إلى HTTPS (اختياري)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # إزالة www (اختياري)
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ http://%1/$1 [R=301,L]
    
    # الروابط الودودة للصور
    RewriteRule ^image/([0-9]+)/?$ image.php?id=$1 [L,QSA]
    RewriteRule ^image/([0-9]+)/([^/]+)/?$ image.php?id=$1 [L,QSA]
    
    # الروابط الودودة للأصناف
    RewriteRule ^category/([0-9]+)/?$ index.php?category=$1 [L,QSA]
    RewriteRule ^category/([^/]+)/?$ index.php?category_slug=$1 [L,QSA]
    
    # الروابط الودودة للعلامات
    RewriteRule ^tag/([0-9]+)/?$ index.php?tag=$1 [L,QSA]
    RewriteRule ^tag/([^/]+)/?$ index.php?tag_slug=$1 [L,QSA]
    
    # صفحة البحث
    RewriteRule ^search/([^/]+)/?$ index.php?search=$1 [L,QSA]
    
    # صفحات ثابتة
    RewriteRule ^gallery/?$ index.php [L,QSA]
    RewriteRule ^about/?$ about.php [L,QSA]
    RewriteRule ^contact/?$ contact.php [L,QSA]
</IfModule>

# ===================================
# حماية المجلدات الحساسة
# Protect Sensitive Directories
# ===================================

# حماية مجلد config
<Directory "config">
    Order Allow,Deny
    Deny from all
</Directory>

# حماية مجلد classes
<Directory "classes">
    Order Allow,Deny
    Deny from all
</Directory>

# حماية مجلد includes
<Directory "includes">
    Order Allow,Deny
    Deny from all
</Directory>

# حماية مجلد database
<Directory "database">
    Order Allow,Deny
    Deny from all
</Directory>

# حماية مجلد install
<Directory "install">
    Order Allow,Deny
    Deny from all
</Directory>

# ===================================
# إعدادات PHP
# PHP Settings
# ===================================

<IfModule mod_php7.c>
    # إعدادات الرفع
    php_value upload_max_filesize 10M
    php_value post_max_size 50M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M
    
    # إعدادات الجلسة
    php_value session.gc_maxlifetime 3600
    php_value session.cookie_lifetime 0
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    
    # إعدادات الأمان
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
    
    # إعدادات الترميز
    php_value default_charset UTF-8
    php_value mbstring.internal_encoding UTF-8
</IfModule>

# ===================================
# إعدادات MIME Types
# MIME Types Settings
# ===================================

<IfModule mod_mime.c>
    # الصور
    AddType image/webp .webp
    AddType image/avif .avif
    
    # الخطوط
    AddType font/woff .woff
    AddType font/woff2 .woff2
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    
    # ملفات أخرى
    AddType application/json .json
    AddType application/ld+json .jsonld
</IfModule>

# ===================================
# إعدادات الأمان الإضافية
# Additional Security Settings
# ===================================

# منع الوصول للملفات المخفية
<FilesMatch "^\.">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# حماية من هجمات Hotlinking
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yourdomain\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|webp)$ - [F]
</IfModule>

# إضافة رؤوس الأمان
<IfModule mod_headers.c>
    # منع XSS
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    # إعدادات المحتوى
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # إزالة معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# ===================================
# صفحات الأخطاء المخصصة
# Custom Error Pages
# ===================================

ErrorDocument 400 /error.php?code=400
ErrorDocument 401 /error.php?code=401
ErrorDocument 403 /error.php?code=403
ErrorDocument 404 /error.php?code=404
ErrorDocument 500 /error.php?code=500

# ===================================
# إعدادات إضافية للأداء
# Additional Performance Settings
# ===================================

# تفعيل KeepAlive
<IfModule mod_headers.c>
    Header set Connection keep-alive
</IfModule>

# ضغط إضافي للملفات
<IfModule mod_gzip.c>
    mod_gzip_on Yes
    mod_gzip_dechunk Yes
    mod_gzip_item_include file \.(html?|txt|css|js|php|pl)$
    mod_gzip_item_include mime ^application/x-javascript.*
    mod_gzip_item_include mime ^text/.*
    mod_gzip_item_exclude rspheader ^Content-Encoding:.*gzip.*
</IfModule>
