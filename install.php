<?php
/**
 * معالج التثبيت التلقائي
 * Automatic Installation Handler
 */

// التحقق من وجود ملف الإعداد
if (file_exists('config/config.php')) {
    header('Location: index.php');
    exit('التثبيت مكتمل بالفعل. يرجى حذف ملف install.php للأمان.');
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$errors = [];
$success = [];

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // التحقق من المتطلبات
            $requirements = checkRequirements();
            if ($requirements['all_passed']) {
                header('Location: install.php?step=2');
                exit;
            } else {
                $errors = $requirements['errors'];
            }
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $dbResult = setupDatabase($_POST);
            if ($dbResult['success']) {
                header('Location: install.php?step=3');
                exit;
            } else {
                $errors = $dbResult['errors'];
            }
            break;
            
        case 3:
            // إنشاء مستخدم الإدارة
            $adminResult = createAdminUser($_POST);
            if ($adminResult['success']) {
                header('Location: install.php?step=4');
                exit;
            } else {
                $errors = $adminResult['errors'];
            }
            break;
            
        case 4:
            // إنهاء التثبيت
            $finalResult = finalizeInstallation($_POST);
            if ($finalResult['success']) {
                header('Location: install.php?step=5');
                exit;
            } else {
                $errors = $finalResult['errors'];
            }
            break;
    }
}

// دالة التحقق من المتطلبات
function checkRequirements() {
    $requirements = [
        'php_version' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'pdo' => extension_loaded('pdo'),
        'pdo_mysql' => extension_loaded('pdo_mysql'),
        'gd' => extension_loaded('gd'),
        'exif' => extension_loaded('exif'),
        'json' => extension_loaded('json'),
        'mbstring' => extension_loaded('mbstring'),
        'uploads_writable' => is_writable('uploads/') || mkdir('uploads/', 0755, true),
        'config_writable' => is_writable('config/') || mkdir('config/', 0755, true)
    ];
    
    $errors = [];
    foreach ($requirements as $req => $passed) {
        if (!$passed) {
            $errors[] = getRequirementError($req);
        }
    }
    
    return [
        'all_passed' => empty($errors),
        'requirements' => $requirements,
        'errors' => $errors
    ];
}

// دالة إعداد قاعدة البيانات
function setupDatabase($data) {
    $errors = [];
    
    // التحقق من البيانات
    $required = ['db_host', 'db_name', 'db_user', 'db_pass'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            $errors[] = "حقل {$field} مطلوب";
        }
    }
    
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    try {
        // اختبار الاتصال
        $pdo = new PDO(
            "mysql:host={$data['db_host']};charset=utf8mb4",
            $data['db_user'],
            $data['db_pass'],
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$data['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$data['db_name']}`");
        
        // تنفيذ ملف SQL
        $sql = file_get_contents('database/schema.sql');
        $pdo->exec($sql);
        
        // حفظ إعدادات قاعدة البيانات
        saveDbConfig($data);
        
        return ['success' => true];
        
    } catch (Exception $e) {
        return ['success' => false, 'errors' => ['خطأ في قاعدة البيانات: ' . $e->getMessage()]];
    }
}

// دالة إنشاء مستخدم الإدارة
function createAdminUser($data) {
    $errors = [];
    
    // التحقق من البيانات
    if (empty($data['admin_username']) || empty($data['admin_password']) || empty($data['admin_email'])) {
        $errors[] = 'جميع حقول مستخدم الإدارة مطلوبة';
    }
    
    if (strlen($data['admin_password']) < 6) {
        $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    if (!filter_var($data['admin_email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    try {
        require_once 'config/config.php';
        require_once 'classes/Database.php';
        
        $db = new Database();
        
        // حذف المستخدم الافتراضي
        $db->delete("DELETE FROM admin_users WHERE username = 'admin'");
        
        // إضافة المستخدم الجديد
        $passwordHash = password_hash($data['admin_password'], PASSWORD_DEFAULT);
        
        $db->insert(
            "INSERT INTO admin_users (username, email, password_hash, full_name, is_active) VALUES (?, ?, ?, ?, 1)",
            [$data['admin_username'], $data['admin_email'], $passwordHash, $data['admin_fullname'] ?? '']
        );
        
        return ['success' => true];
        
    } catch (Exception $e) {
        return ['success' => false, 'errors' => ['خطأ في إنشاء المستخدم: ' . $e->getMessage()]];
    }
}

// دالة إنهاء التثبيت
function finalizeInstallation($data) {
    try {
        // إنشاء المجلدات المطلوبة
        $directories = [
            'uploads',
            'uploads/thumbnails',
            'uploads/medium',
            'uploads/temp'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        // إنشاء ملف .htaccess للحماية
        $htaccess = "Options -Indexes\n";
        $htaccess .= "RewriteEngine On\n";
        $htaccess .= "RewriteCond %{REQUEST_FILENAME} !-f\n";
        $htaccess .= "RewriteCond %{REQUEST_FILENAME} !-d\n";
        $htaccess .= "RewriteRule ^(.*)$ index.php [QSA,L]\n";
        
        file_put_contents('.htaccess', $htaccess);
        
        // إنشاء ملف robots.txt
        $robots = "User-agent: *\n";
        $robots .= "Allow: /\n";
        $robots .= "Disallow: /admin/\n";
        $robots .= "Disallow: /config/\n";
        $robots .= "Disallow: /classes/\n";
        $robots .= "Disallow: /includes/\n";
        $robots .= "Disallow: /database/\n";
        
        file_put_contents('robots.txt', $robots);
        
        return ['success' => true];
        
    } catch (Exception $e) {
        return ['success' => false, 'errors' => ['خطأ في إنهاء التثبيت: ' . $e->getMessage()]];
    }
}

// دالة حفظ إعدادات قاعدة البيانات
function saveDbConfig($data) {
    $config = "<?php\n";
    $config .= "/**\n";
    $config .= " * ملف إعدادات قاعدة البيانات\n";
    $config .= " * Database Configuration File\n";
    $config .= " * تم إنشاؤه تلقائياً بواسطة معالج التثبيت\n";
    $config .= " * Generated automatically by installation wizard\n";
    $config .= " */\n\n";
    
    $config .= "// إعدادات قاعدة البيانات\n";
    $config .= "define('DB_HOST', '" . addslashes($data['db_host']) . "');\n";
    $config .= "define('DB_NAME', '" . addslashes($data['db_name']) . "');\n";
    $config .= "define('DB_USER', '" . addslashes($data['db_user']) . "');\n";
    $config .= "define('DB_PASS', '" . addslashes($data['db_pass']) . "');\n";
    $config .= "define('DB_CHARSET', 'utf8mb4');\n\n";
    
    $config .= "// إعدادات الموقع\n";
    $config .= "define('SITE_NAME', '" . addslashes($data['site_name'] ?? 'معرض الصور') . "');\n";
    $config .= "define('SITE_DESCRIPTION', '" . addslashes($data['site_description'] ?? 'معرض صور احترافي متعدد اللغات') . "');\n";
    $config .= "define('DEFAULT_LANGUAGE', '" . ($data['default_language'] ?? 'ar') . "');\n\n";
    
    $config .= "// إعدادات الأمان\n";
    $config .= "define('SECURITY_SALT', '" . bin2hex(random_bytes(32)) . "');\n";
    $config .= "define('DEBUG_MODE', false);\n\n";
    
    $config .= "// إعدادات الصور\n";
    $config .= "define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB\n";
    $config .= "define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'webp']);\n";
    $config .= "define('THUMBNAIL_WIDTH', 300);\n";
    $config .= "define('THUMBNAIL_HEIGHT', 300);\n";
    $config .= "define('MEDIUM_WIDTH', 800);\n";
    $config .= "define('MEDIUM_HEIGHT', 600);\n\n";
    
    $config .= "// إعدادات العرض\n";
    $config .= "define('IMAGES_PER_PAGE', 12);\n";
    $config .= "define('ADMIN_IMAGES_PER_PAGE', 20);\n\n";
    
    $config .= "// مسارات الملفات\n";
    $config .= "define('ROOT_PATH', __DIR__);\n";
    $config .= "define('UPLOADS_DIR', 'uploads/');\n";
    $config .= "define('THUMBNAILS_DIR', 'uploads/thumbnails/');\n";
    $config .= "define('MEDIUM_DIR', 'uploads/medium/');\n";
    $config .= "define('TEMP_DIR', 'uploads/temp/');\n\n";
    
    $config .= "// روابط الملفات\n";
    $config .= "define('BASE_URL', 'http" . (isset($_SERVER['HTTPS']) ? 's' : '') . "://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/');\n";
    $config .= "define('UPLOADS_URL', BASE_URL . UPLOADS_DIR);\n";
    $config .= "define('THUMBNAILS_URL', BASE_URL . THUMBNAILS_DIR);\n";
    $config .= "define('MEDIUM_URL', BASE_URL . MEDIUM_DIR);\n\n";
    
    $config .= "// إعدادات الجلسة\n";
    $config .= "define('SESSION_TIMEOUT', 3600); // ساعة واحدة\n";
    $config .= "define('REMEMBER_ME_DURATION', 30 * 24 * 3600); // 30 يوم\n\n";
    
    $config .= "// إعدادات الأمان\n";
    $config .= "define('MAX_LOGIN_ATTEMPTS', 5);\n";
    $config .= "define('LOGIN_LOCKOUT_TIME', 15 * 60); // 15 دقيقة\n\n";
    
    $config .= "// إعدادات SEO\n";
    $config .= "define('META_TITLE_SEPARATOR', ' | ');\n";
    $config .= "define('DEFAULT_META_DESCRIPTION', 'معرض صور احترافي متعدد اللغات');\n";
    $config .= "define('DEFAULT_META_KEYWORDS', 'صور, معرض, تصوير, فوتوغرافيا');\n\n";
    
    $config .= "?>";
    
    file_put_contents('config/config.php', $config);
}

// دالة الحصول على رسالة خطأ المتطلب
function getRequirementError($requirement) {
    $errors = [
        'php_version' => 'إصدار PHP يجب أن يكون 7.4 أو أحدث',
        'pdo' => 'امتداد PDO غير مثبت',
        'pdo_mysql' => 'امتداد PDO MySQL غير مثبت',
        'gd' => 'امتداد GD غير مثبت (مطلوب لمعالجة الصور)',
        'exif' => 'امتداد EXIF غير مثبت (مطلوب لقراءة بيانات الصور)',
        'json' => 'امتداد JSON غير مثبت',
        'mbstring' => 'امتداد mbstring غير مثبت (مطلوب للنصوص متعددة البايت)',
        'uploads_writable' => 'مجلد uploads غير قابل للكتابة',
        'config_writable' => 'مجلد config غير قابل للكتابة'
    ];
    
    return $errors[$requirement] ?? "متطلب غير معروف: {$requirement}";
}

$pageTitle = "تثبيت معرض الصور - الخطوة {$step}";
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .install-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .install-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
            position: relative;
        }
        
        .step.active {
            background: #007bff;
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 100%;
            top: 50%;
            width: 1rem;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }
        
        .step.completed:not(:last-child)::after {
            background: #28a745;
        }
        
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .requirement-item:last-child {
            border-bottom: none;
        }
        
        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .status-pass {
            background: #28a745;
        }
        
        .status-fail {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <div class="install-header">
                <h1 class="h2 mb-0">
                    <i class="fas fa-camera me-2"></i>
                    معرض الصور
                </h1>
                <p class="mb-0 opacity-75">معالج التثبيت التلقائي</p>
            </div>
            
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? ($step > 4 ? 'completed' : 'active') : ''; ?>">4</div>
                <div class="step <?php echo $step >= 5 ? 'completed' : ''; ?>">5</div>
            </div>
            
            <div class="p-4">
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>حدثت أخطاء:</h6>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>نجح:</h6>
                    <ul class="mb-0">
                        <?php foreach ($success as $msg): ?>
                        <li><?php echo htmlspecialchars($msg); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <?php
                switch ($step) {
                    case 1:
                        include 'install/step1-requirements.php';
                        break;
                    case 2:
                        include 'install/step2-database.php';
                        break;
                    case 3:
                        include 'install/step3-admin.php';
                        break;
                    case 4:
                        include 'install/step4-settings.php';
                        break;
                    case 5:
                        include 'install/step5-complete.php';
                        break;
                    default:
                        echo '<div class="alert alert-danger">خطوة غير صحيحة</div>';
                }
                ?>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
