<?php
/**
 * مولد خريطة الموقع التلقائية
 * Automatic Sitemap Generator
 */

require_once 'init.php';

// تعيين نوع المحتوى
header('Content-Type: application/xml; charset=utf-8');

// الحصول على البيانات
$db = new Database();

// الصور النشطة
$images = $db->fetchAll("
    SELECT id, title, title_ar, upload_date, updated_at 
    FROM images 
    WHERE is_active = 1 
    ORDER BY upload_date DESC
");

// الأصناف النشطة
$categories = $db->fetchAll("
    SELECT id, name, name_ar, slug, updated_at 
    FROM categories 
    WHERE is_active = 1 
    ORDER BY sort_order
");

// العلامات النشطة
$tags = $db->fetchAll("
    SELECT id, name, name_ar, slug, updated_at 
    FROM tags 
    WHERE is_active = 1 AND usage_count > 0 
    ORDER BY usage_count DESC
");

// دالة تنسيق التاريخ
function formatDate($date) {
    return date('c', strtotime($date));
}

// دالة إنشاء URL
function createUrl($path) {
    return rtrim(BASE_URL, '/') . '/' . ltrim($path, '/');
}

echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">

    <!-- الصفحة الرئيسية | Homepage -->
    <url>
        <loc><?php echo createUrl(''); ?></loc>
        <lastmod><?php echo formatDate(date('Y-m-d H:i:s')); ?></lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
        <xhtml:link rel="alternate" hreflang="ar" href="<?php echo createUrl('?lang=ar'); ?>" />
        <xhtml:link rel="alternate" hreflang="en" href="<?php echo createUrl('?lang=en'); ?>" />
    </url>

    <!-- صفحة المعرض | Gallery Page -->
    <url>
        <loc><?php echo createUrl('gallery/'); ?></loc>
        <lastmod><?php echo formatDate(date('Y-m-d H:i:s')); ?></lastmod>
        <changefreq>daily</changefreq>
        <priority>0.9</priority>
        <xhtml:link rel="alternate" hreflang="ar" href="<?php echo createUrl('gallery/?lang=ar'); ?>" />
        <xhtml:link rel="alternate" hreflang="en" href="<?php echo createUrl('gallery/?lang=en'); ?>" />
    </url>

    <!-- صفحة الأصناف | Categories Page -->
    <url>
        <loc><?php echo createUrl('categories/'); ?></loc>
        <lastmod><?php echo formatDate(date('Y-m-d H:i:s')); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
        <xhtml:link rel="alternate" hreflang="ar" href="<?php echo createUrl('categories/?lang=ar'); ?>" />
        <xhtml:link rel="alternate" hreflang="en" href="<?php echo createUrl('categories/?lang=en'); ?>" />
    </url>

    <!-- صفحة العلامات | Tags Page -->
    <url>
        <loc><?php echo createUrl('tags/'); ?></loc>
        <lastmod><?php echo formatDate(date('Y-m-d H:i:s')); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.7</priority>
        <xhtml:link rel="alternate" hreflang="ar" href="<?php echo createUrl('tags/?lang=ar'); ?>" />
        <xhtml:link rel="alternate" hreflang="en" href="<?php echo createUrl('tags/?lang=en'); ?>" />
    </url>

    <?php foreach ($images as $image): ?>
    <!-- صفحة الصورة: <?php echo htmlspecialchars($image['title'] ?: $image['title_ar']); ?> -->
    <url>
        <loc><?php echo createUrl("image/{$image['id']}/"); ?></loc>
        <lastmod><?php echo formatDate($image['updated_at']); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.6</priority>
        <xhtml:link rel="alternate" hreflang="ar" href="<?php echo createUrl("image/{$image['id']}/?lang=ar"); ?>" />
        <xhtml:link rel="alternate" hreflang="en" href="<?php echo createUrl("image/{$image['id']}/?lang=en"); ?>" />
        
        <!-- معلومات الصورة | Image Information -->
        <image:image>
            <image:loc><?php echo createUrl("uploads/{$image['filename']}"); ?></image:loc>
            <?php if ($image['title']): ?>
            <image:title><?php echo htmlspecialchars($image['title']); ?></image:title>
            <?php endif; ?>
            <?php if ($image['title_ar']): ?>
            <image:caption><?php echo htmlspecialchars($image['title_ar']); ?></image:caption>
            <?php endif; ?>
        </image:image>
    </url>
    <?php endforeach; ?>

    <?php foreach ($categories as $category): ?>
    <!-- صفحة الصنف: <?php echo htmlspecialchars($category['name_ar']); ?> -->
    <url>
        <loc><?php echo createUrl("category/{$category['slug']}/"); ?></loc>
        <lastmod><?php echo formatDate($category['updated_at']); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.7</priority>
        <xhtml:link rel="alternate" hreflang="ar" href="<?php echo createUrl("category/{$category['slug']}/?lang=ar"); ?>" />
        <xhtml:link rel="alternate" hreflang="en" href="<?php echo createUrl("category/{$category['slug']}/?lang=en"); ?>" />
    </url>
    <?php endforeach; ?>

    <?php foreach ($tags as $tag): ?>
    <!-- صفحة العلامة: <?php echo htmlspecialchars($tag['name_ar']); ?> -->
    <url>
        <loc><?php echo createUrl("tag/{$tag['slug']}/"); ?></loc>
        <lastmod><?php echo formatDate($tag['updated_at']); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.5</priority>
        <xhtml:link rel="alternate" hreflang="ar" href="<?php echo createUrl("tag/{$tag['slug']}/?lang=ar"); ?>" />
        <xhtml:link rel="alternate" hreflang="en" href="<?php echo createUrl("tag/{$tag['slug']}/?lang=en"); ?>" />
    </url>
    <?php endforeach; ?>

</urlset>
