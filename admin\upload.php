<?php
/**
 * صفحة رفع الصور
 * Image Upload Page
 */

require_once '../init.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$currentUser = $auth->getCurrentUser();

// إنشاء كائنات الإدارة
$imageManager = new ImageManager();
$categoryManager = new CategoryManager();
$tagManager = new TagManager();
$fileUploader = new FileUploader();
$imageProcessor = new ImageProcessor();

$message = '';
$messageType = '';

// معالجة رفع الصور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['images'])) {
    $uploadedImages = [];
    $errors = [];
    
    // معالجة الملفات المرفوعة
    if (is_array($_FILES['images']['name'])) {
        // رفع متعدد
        $results = $fileUploader->uploadMultipleImages($_FILES['images']);
    } else {
        // رفع مفرد
        $result = $fileUploader->uploadImage($_FILES['images']);
        $results = $result ? [$result] : [];
    }
    
    if (!empty($results)) {
        foreach ($results as $result) {
            // إضافة الصورة إلى قاعدة البيانات
            $imageData = [
                'filename' => $result['filename'],
                'original_filename' => $result['original_filename'],
                'title' => $_POST['title'] ?? '',
                'title_ar' => $_POST['title_ar'] ?? '',
                'description' => $_POST['description'] ?? '',
                'description_ar' => $_POST['description_ar'] ?? '',
                'alt_text' => $_POST['alt_text'] ?? '',
                'alt_text_ar' => $_POST['alt_text_ar'] ?? '',
                'file_size' => $result['file_size'],
                'width' => $result['width'],
                'height' => $result['height'],
                'mime_type' => $result['mime_type'],
                'file_path' => $result['file_path'],
                'thumbnail_path' => $result['thumbnail_path'],
                'medium_path' => $result['medium_path'],
                'category_id' => !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
                'sort_order' => (int)($_POST['sort_order'] ?? 0)
            ];
            
            $imageId = $imageManager->addImage($imageData);
            
            if ($imageId) {
                // إضافة بيانات EXIF
                if (!empty($result['exif_data'])) {
                    $exifData = $result['exif_data'];
                    $exifData['image_id'] = $imageId;
                    
                    $exifQuery = "INSERT INTO image_exif (image_id, camera_make, camera_model, lens_model, 
                                  focal_length, aperture, shutter_speed, iso, flash, date_taken, 
                                  gps_latitude, gps_longitude, orientation, color_space, white_balance, 
                                  exposure_mode, metering_mode) 
                                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    
                    $db->insert($exifQuery, [
                        $imageId,
                        $exifData['camera_make'] ?? null,
                        $exifData['camera_model'] ?? null,
                        $exifData['lens_model'] ?? null,
                        $exifData['focal_length'] ?? null,
                        $exifData['aperture'] ?? null,
                        $exifData['shutter_speed'] ?? null,
                        $exifData['iso'] ?? null,
                        $exifData['flash'] ?? null,
                        $exifData['date_taken'] ?? null,
                        $exifData['gps_latitude'] ?? null,
                        $exifData['gps_longitude'] ?? null,
                        $exifData['orientation'] ?? null,
                        $exifData['color_space'] ?? null,
                        $exifData['white_balance'] ?? null,
                        $exifData['exposure_mode'] ?? null,
                        $exifData['metering_mode'] ?? null
                    ]);
                }
                
                // إضافة العلامات
                if (!empty($_POST['tags'])) {
                    $tagIds = $tagManager->createTagsFromText($_POST['tags']);
                    if (!empty($tagIds)) {
                        $imageManager->addImageTags($imageId, $tagIds);
                    }
                }
                
                $uploadedImages[] = $imageId;
            }
        }
        
        if (!empty($uploadedImages)) {
            $message = __('images_uploaded_successfully') . ' (' . count($uploadedImages) . ')';
            $messageType = 'success';
        }
    }
    
    // جمع الأخطاء
    $errors = array_merge($errors, $fileUploader->getErrors());
    
    if (!empty($errors)) {
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }
}

// الحصول على الأصناف للقائمة المنسدلة
$categories = $categoryManager->getCategories();

$pageTitle = __('upload_images') . META_TITLE_SEPARATOR . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="<?php echo $currentLang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dropzone CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
    <?php if ($isRTL): ?>
    <link href="../assets/css/rtl.css" rel="stylesheet">
    <link href="assets/css/admin-rtl.css" rel="stylesheet">
    <?php endif; ?>
</head>
<body class="admin-body">
    <!-- Navigation -->
    <?php include 'includes/nav.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1"><?php echo __('upload_images'); ?></h1>
                <p class="text-muted mb-0"><?php echo __('upload_description'); ?></p>
            </div>
            <div>
                <a href="images.php" class="btn btn-outline-secondary">
                    <i class="fas fa-list me-1"></i><?php echo __('view_images'); ?>
                </a>
            </div>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <div class="row">
            <!-- Upload Area -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cloud-upload-alt me-2"></i><?php echo __('select_images'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Dropzone -->
                        <div id="image-dropzone" class="dropzone">
                            <div class="dz-message">
                                <div class="dz-icon">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted"></i>
                                </div>
                                <h4><?php echo __('drag_drop_images'); ?></h4>
                                <p class="text-muted">
                                    <?php echo __('or_click_to_select'); ?><br>
                                    <small>
                                        <?php echo __('supported_formats'); ?>: JPG, PNG, GIF, WebP<br>
                                        <?php echo __('max_file_size'); ?>: <?php echo formatFileSize(MAX_FILE_SIZE); ?>
                                    </small>
                                </p>
                            </div>
                        </div>
                        
                        <!-- Traditional Upload Form (Fallback) -->
                        <div id="traditional-upload" style="display: none;">
                            <form method="POST" enctype="multipart/form-data" id="uploadForm">
                                <div class="mb-3">
                                    <label for="images" class="form-label"><?php echo __('select_images'); ?></label>
                                    <input type="file" 
                                           class="form-control" 
                                           id="images" 
                                           name="images[]" 
                                           accept="image/*" 
                                           multiple 
                                           required>
                                </div>
                                
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload me-1"></i><?php echo __('upload'); ?>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Image Details Form -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('image_details'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="imageDetailsForm">
                            <div class="mb-3">
                                <label for="title" class="form-label"><?php echo __('title'); ?> (<?php echo __('english'); ?>)</label>
                                <input type="text" class="form-control" id="title" name="title">
                            </div>
                            
                            <div class="mb-3">
                                <label for="title_ar" class="form-label"><?php echo __('title'); ?> (<?php echo __('arabic'); ?>)</label>
                                <input type="text" class="form-control" id="title_ar" name="title_ar">
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label"><?php echo __('description'); ?> (<?php echo __('english'); ?>)</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description_ar" class="form-label"><?php echo __('description'); ?> (<?php echo __('arabic'); ?>)</label>
                                <textarea class="form-control" id="description_ar" name="description_ar" rows="3"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="category_id" class="form-label"><?php echo __('category'); ?></label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value=""><?php echo __('select_category'); ?></option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>">
                                        <?php echo htmlspecialchars($currentLang === 'ar' ? $category['name_ar'] : $category['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="tags" class="form-label"><?php echo __('tags'); ?></label>
                                <input type="text" 
                                       class="form-control" 
                                       id="tags" 
                                       name="tags" 
                                       placeholder="<?php echo __('separate_tags_with_commas'); ?>">
                                <div class="form-text"><?php echo __('tags_help_text'); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="alt_text" class="form-label"><?php echo __('alt_text'); ?> (<?php echo __('english'); ?>)</label>
                                <input type="text" class="form-control" id="alt_text" name="alt_text">
                            </div>
                            
                            <div class="mb-3">
                                <label for="alt_text_ar" class="form-label"><?php echo __('alt_text'); ?> (<?php echo __('arabic'); ?>)</label>
                                <input type="text" class="form-control" id="alt_text_ar" name="alt_text_ar">
                            </div>
                            
                            <div class="mb-3">
                                <label for="sort_order" class="form-label"><?php echo __('sort_order'); ?></label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" value="0">
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Upload Progress -->
                <div class="card mt-3" id="upload-progress" style="display: none;">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-progress-bar me-2"></i><?php echo __('upload_progress'); ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-2">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" 
                                 style="width: 0%" 
                                 id="progress-bar"></div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small class="text-muted" id="progress-text">0%</small>
                            <small class="text-muted" id="progress-files">0 / 0</small>
                        </div>
                    </div>
                </div>

                <!-- Upload Tips -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-lightbulb me-2"></i><?php echo __('upload_tips'); ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <?php echo __('tip_file_formats'); ?>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <?php echo __('tip_file_size'); ?>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <?php echo __('tip_multiple_upload'); ?>
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-check text-success me-2"></i>
                                <?php echo __('tip_auto_thumbnails'); ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"></script>
    <script src="../assets/js/gallery.js"></script>
    <script src="assets/js/admin.js"></script>
    <script src="assets/js/upload.js"></script>
</body>
</html>
