/**
 * ملف الأنماط الرئيسي لمعرض الصور
 * Main Stylesheet for Photo Gallery
 */

/* ===== متغيرات CSS ===== */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* ===== الأنماط العامة ===== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* دعم الخطوط العربية */
body[dir="rtl"] {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

/* ===== التنقل ===== */
.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
    transform: translateY(-1px);
}

/* ===== البطاقات ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* ===== معرض الصور ===== */
.gallery-item {
    overflow: hidden;
    transition: var(--transition);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.2);
}

.gallery-thumbnail {
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-item:hover .gallery-thumbnail {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-actions .btn {
    margin: 0 0.25rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* ===== صفحة الصورة المفردة ===== */
#main-image {
    max-height: 70vh;
    object-fit: contain;
    background: #000;
}

.image-actions-overlay {
    position: absolute;
    top: 1rem;
    right: 1rem;
    opacity: 0;
    transition: var(--transition);
}

.card:hover .image-actions-overlay {
    opacity: 1;
}

.exif-data .mb-2:last-child {
    margin-bottom: 0 !important;
}

/* ===== العلامات ===== */
.tag-cloud .tag-item {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    transition: var(--transition);
}

.tag-cloud .tag-item:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
    transform: translateY(-1px);
}

.tag-list .badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    transition: var(--transition);
}

.tag-list .badge:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* ===== التصفح (Pagination) ===== */
.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 0.125rem;
    transition: var(--transition);
}

.pagination .page-link:hover {
    transform: translateY(-1px);
}

.pagination-info {
    font-size: 0.875rem;
}

/* ===== الشريط الجانبي ===== */
.list-group-item {
    border: none;
    border-radius: var(--border-radius) !important;
    margin-bottom: 0.25rem;
    transition: var(--transition);
}

.list-group-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
}

.list-group-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* ===== الأزرار ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-group .btn {
    transform: none;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
}

/* ===== النماذج ===== */
.form-control {
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

/* ===== التنبيهات ===== */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

/* ===== التحميل ===== */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== الاستجابة للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .gallery-thumbnail {
        height: 200px;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .image-actions-overlay {
        opacity: 1;
        background: rgba(0, 0, 0, 0.5);
        border-radius: var(--border-radius);
    }
    
    #main-image {
        max-height: 50vh;
    }
}

@media (max-width: 576px) {
    .gallery-thumbnail {
        height: 180px;
    }
    
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* ===== تأثيرات خاصة ===== */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* ===== تحسينات إضافية ===== */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.bg-gradient-dark {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%);
}

/* ===== تخصيص Lightbox ===== */
.lb-data .lb-caption {
    font-size: 1rem;
    font-weight: 500;
    color: #fff;
}

.lb-data .lb-number {
    color: #ccc;
}

/* ===== تخصيص Scrollbar ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .navbar,
    .btn,
    .pagination,
    .gallery-overlay,
    .image-actions-overlay {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    body {
        background: white;
    }
}

/* ===== حالات التركيز للوصولية ===== */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== تحسينات الأداء ===== */
.gallery-thumbnail,
#main-image {
    will-change: transform;
}

.card,
.btn,
.gallery-item {
    will-change: transform, box-shadow;
}

/* ===== متغيرات الألوان للوضع المظلم (اختياري) ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #e0e0e0;
        --card-bg: #2d2d2d;
        --border-color: #404040;
    }
    
    /* يمكن تطبيق الوضع المظلم هنا إذا رغبت */
}
