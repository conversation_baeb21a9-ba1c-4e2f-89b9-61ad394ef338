<?php
/**
 * الصفحة الرئيسية لمعرض الصور
 * Photo Gallery Main Page
 */

require_once 'init.php';

// إنشاء كائنات الإدارة
$imageManager = new ImageManager();
$categoryManager = new CategoryManager();
$tagManager = new TagManager();

// الحصول على المعاملات
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$categoryId = isset($_GET['category']) ? (int)$_GET['category'] : null;
$tagId = isset($_GET['tag']) ? (int)$_GET['tag'] : null;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'newest';

// إعداد التصفية
$filters = [];
if ($categoryId) $filters['category_id'] = $categoryId;
if ($tagId) $filters['tag_id'] = $tagId;
if ($search) $filters['search'] = $search;
if ($sort) $filters['sort'] = $sort;

// الحصول على الصور
$images = $imageManager->getImages($filters, $page, IMAGES_PER_PAGE);
$totalImages = $imageManager->countImages($filters);

// إنشاء التصفح
$pagination = new Pagination($totalImages, IMAGES_PER_PAGE, $page, 'index.php', $_GET);

// الحصول على الأصناف والعلامات للتصفية
$categories = $categoryManager->getCategoriesWithImageCount();
$popularTags = $tagManager->getPopularTags(20);

// الحصول على الصنف أو العلامة المحددة
$selectedCategory = $categoryId ? $categoryManager->getCategory($categoryId) : null;
$selectedTag = $tagId ? $tagManager->getTag($tagId) : null;

// إعداد العنوان والوصف
$pageTitle = SITE_NAME;
$pageDescription = DEFAULT_META_DESCRIPTION;

if ($selectedCategory) {
    $pageTitle = ($currentLang === 'ar' ? $selectedCategory['name_ar'] : $selectedCategory['name']) . META_TITLE_SEPARATOR . SITE_NAME;
    $pageDescription = $currentLang === 'ar' ? $selectedCategory['description_ar'] : $selectedCategory['description'];
} elseif ($selectedTag) {
    $pageTitle = ($currentLang === 'ar' ? $selectedTag['name_ar'] : $selectedTag['name']) . META_TITLE_SEPARATOR . SITE_NAME;
} elseif ($search) {
    $pageTitle = __('search') . ': ' . $search . META_TITLE_SEPARATOR . SITE_NAME;
}

// الحصول على الصور الشائعة للشريط الجانبي
$popularImages = $imageManager->getPopularImages(6);
$recentImages = $imageManager->getRecentImages(6);
?>
<!DOCTYPE html>
<html lang="<?php echo $currentLang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="<?php echo DEFAULT_META_KEYWORDS; ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Lightbox CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <?php if ($isRTL): ?>
    <link href="assets/css/rtl.css" rel="stylesheet">
    <?php endif; ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-camera me-2"></i>
                <?php echo SITE_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo empty($_GET) ? 'active' : ''; ?>" href="index.php">
                            <i class="fas fa-home me-1"></i><?php echo __('home'); ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-folder me-1"></i><?php echo __('categories'); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="index.php"><?php echo __('all_categories'); ?></a></li>
                            <li><hr class="dropdown-divider"></li>
                            <?php foreach ($categories as $category): ?>
                            <li>
                                <a class="dropdown-item" href="index.php?category=<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($currentLang === 'ar' ? $category['name_ar'] : $category['name']); ?>
                                    <span class="badge bg-secondary ms-2"><?php echo $category['image_count']; ?></span>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </li>
                </ul>
                
                <!-- Search Form -->
                <form class="d-flex me-3" method="GET" action="index.php">
                    <?php if ($categoryId): ?>
                    <input type="hidden" name="category" value="<?php echo $categoryId; ?>">
                    <?php endif; ?>
                    <?php if ($tagId): ?>
                    <input type="hidden" name="tag" value="<?php echo $tagId; ?>">
                    <?php endif; ?>
                    <input class="form-control me-2" type="search" name="search" 
                           placeholder="<?php echo __('search_placeholder'); ?>" 
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                
                <!-- Language Switcher -->
                <div class="dropdown">
                    <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        <?php echo strtoupper($currentLang); ?>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
                        <li><a class="dropdown-item" href="?lang=en">English</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-filter me-2"></i><?php echo __('filter_by_category'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <a href="index.php" class="list-group-item list-group-item-action <?php echo !$categoryId ? 'active' : ''; ?>">
                                <?php echo __('all_categories'); ?>
                                <span class="badge bg-primary rounded-pill float-end"><?php echo $totalImages; ?></span>
                            </a>
                            <?php foreach ($categories as $category): ?>
                            <a href="index.php?category=<?php echo $category['id']; ?>" 
                               class="list-group-item list-group-item-action <?php echo $categoryId == $category['id'] ? 'active' : ''; ?>">
                                <?php echo htmlspecialchars($currentLang === 'ar' ? $category['name_ar'] : $category['name']); ?>
                                <span class="badge bg-secondary rounded-pill float-end"><?php echo $category['image_count']; ?></span>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Popular Tags -->
                <?php if (!empty($popularTags)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tags me-2"></i><?php echo __('popular_tags'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="tag-cloud">
                            <?php foreach ($popularTags as $tag): ?>
                            <a href="index.php?tag=<?php echo $tag['id']; ?>" 
                               class="badge bg-light text-dark me-1 mb-1 text-decoration-none tag-item <?php echo $tagId == $tag['id'] ? 'bg-primary text-white' : ''; ?>">
                                <?php echo htmlspecialchars($currentLang === 'ar' ? $tag['name_ar'] : $tag['name']); ?>
                                <span class="small">(<?php echo $tag['usage_count']; ?>)</span>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Recent Images -->
                <?php if (!empty($recentImages) && empty($filters)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock me-2"></i><?php echo __('recent_uploads'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <?php foreach ($recentImages as $recentImage): ?>
                            <div class="col-6">
                                <a href="<?php echo UPLOADS_URL . $recentImage['file_path']; ?>" 
                                   data-lightbox="recent" 
                                   data-title="<?php echo htmlspecialchars($currentLang === 'ar' ? $recentImage['title_ar'] : $recentImage['title']); ?>">
                                    <img src="<?php echo THUMBNAILS_URL . basename($recentImage['thumbnail_path']); ?>" 
                                         class="img-fluid rounded" 
                                         alt="<?php echo htmlspecialchars($currentLang === 'ar' ? $recentImage['alt_text_ar'] : $recentImage['alt_text']); ?>">
                                </a>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Main Gallery -->
            <div class="col-lg-9 col-md-8">
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-1">
                            <?php if ($selectedCategory): ?>
                                <i class="fas fa-folder me-2"></i>
                                <?php echo htmlspecialchars($currentLang === 'ar' ? $selectedCategory['name_ar'] : $selectedCategory['name']); ?>
                            <?php elseif ($selectedTag): ?>
                                <i class="fas fa-tag me-2"></i>
                                <?php echo htmlspecialchars($currentLang === 'ar' ? $selectedTag['name_ar'] : $selectedTag['name']); ?>
                            <?php elseif ($search): ?>
                                <i class="fas fa-search me-2"></i>
                                <?php echo __('search'); ?>: <?php echo htmlspecialchars($search); ?>
                            <?php else: ?>
                                <i class="fas fa-images me-2"></i>
                                <?php echo __('gallery'); ?>
                            <?php endif; ?>
                        </h1>
                        <p class="text-muted mb-0">
                            <?php echo $totalImages; ?> <?php echo __('images'); ?>
                        </p>
                    </div>
                    
                    <!-- Sort Options -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-sort me-1"></i><?php echo __('sort_by'); ?>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item <?php echo $sort === 'newest' ? 'active' : ''; ?>" 
                                   href="<?php echo createUrl('index.php', array_merge($_GET, ['sort' => 'newest'])); ?>">
                                <?php echo __('sort_newest'); ?></a></li>
                            <li><a class="dropdown-item <?php echo $sort === 'oldest' ? 'active' : ''; ?>" 
                                   href="<?php echo createUrl('index.php', array_merge($_GET, ['sort' => 'oldest'])); ?>">
                                <?php echo __('sort_oldest'); ?></a></li>
                            <li><a class="dropdown-item <?php echo $sort === 'most_viewed' ? 'active' : ''; ?>" 
                                   href="<?php echo createUrl('index.php', array_merge($_GET, ['sort' => 'most_viewed'])); ?>">
                                <?php echo __('sort_most_viewed'); ?></a></li>
                            <li><a class="dropdown-item <?php echo $sort === 'title' ? 'active' : ''; ?>" 
                                   href="<?php echo createUrl('index.php', array_merge($_GET, ['sort' => 'title'])); ?>">
                                <?php echo __('sort_title'); ?></a></li>
                        </ul>
                    </div>
                </div>

                <!-- Active Filters -->
                <?php if (!empty($filters)): ?>
                <div class="mb-3">
                    <div class="d-flex flex-wrap align-items-center">
                        <span class="me-2"><?php echo __('active_filters'); ?>:</span>
                        <?php if ($selectedCategory): ?>
                        <span class="badge bg-primary me-2">
                            <?php echo htmlspecialchars($currentLang === 'ar' ? $selectedCategory['name_ar'] : $selectedCategory['name']); ?>
                            <a href="<?php echo createUrl('index.php', array_diff_key($_GET, ['category' => ''])); ?>" class="text-white ms-1">×</a>
                        </span>
                        <?php endif; ?>
                        <?php if ($selectedTag): ?>
                        <span class="badge bg-info me-2">
                            <?php echo htmlspecialchars($currentLang === 'ar' ? $selectedTag['name_ar'] : $selectedTag['name']); ?>
                            <a href="<?php echo createUrl('index.php', array_diff_key($_GET, ['tag' => ''])); ?>" class="text-white ms-1">×</a>
                        </span>
                        <?php endif; ?>
                        <?php if ($search): ?>
                        <span class="badge bg-warning me-2">
                            <?php echo htmlspecialchars($search); ?>
                            <a href="<?php echo createUrl('index.php', array_diff_key($_GET, ['search' => ''])); ?>" class="text-dark ms-1">×</a>
                        </span>
                        <?php endif; ?>
                        <a href="index.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-times me-1"></i><?php echo __('clear_filters'); ?>
                        </a>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Images Grid -->
                <?php if (!empty($images)): ?>
                <div class="row g-3" id="gallery-grid">
                    <?php foreach ($images as $image): ?>
                    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6">
                        <div class="card gallery-item">
                            <div class="position-relative">
                                <a href="<?php echo UPLOADS_URL . $image['file_path']; ?>" 
                                   data-lightbox="gallery" 
                                   data-title="<?php echo htmlspecialchars($currentLang === 'ar' ? $image['title_ar'] : $image['title']); ?>">
                                    <img src="<?php echo THUMBNAILS_URL . basename($image['thumbnail_path']); ?>" 
                                         class="card-img-top gallery-thumbnail" 
                                         alt="<?php echo htmlspecialchars($currentLang === 'ar' ? $image['alt_text_ar'] : $image['alt_text']); ?>"
                                         loading="lazy">
                                </a>
                                <div class="gallery-overlay">
                                    <div class="gallery-actions">
                                        <a href="<?php echo UPLOADS_URL . $image['file_path']; ?>" 
                                           data-lightbox="gallery" 
                                           class="btn btn-light btn-sm">
                                            <i class="fas fa-search-plus"></i>
                                        </a>
                                        <a href="image.php?id=<?php echo $image['id']; ?>" 
                                           class="btn btn-light btn-sm">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-2">
                                <h6 class="card-title mb-1 text-truncate">
                                    <?php echo htmlspecialchars($currentLang === 'ar' ? $image['title_ar'] : $image['title']); ?>
                                </h6>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i><?php echo $image['views_count']; ?>
                                    </small>
                                    <?php if ($image['category_name']): ?>
                                    <small class="text-muted">
                                        <a href="index.php?category=<?php echo $image['category_id']; ?>" 
                                           class="text-decoration-none">
                                            <?php echo htmlspecialchars($currentLang === 'ar' ? $image['category_name_ar'] : $image['category_name']); ?>
                                        </a>
                                    </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <div class="mt-4">
                    <?php echo $pagination->render(); ?>
                </div>
                
                <?php else: ?>
                <!-- No Results -->
                <div class="text-center py-5">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h4><?php echo __('no_results'); ?></h4>
                    <p class="text-muted"><?php echo __('try_different_search'); ?></p>
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-home me-1"></i><?php echo __('back_to_gallery'); ?>
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?php echo SITE_NAME; ?></h5>
                    <p><?php echo SITE_DESCRIPTION; ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. <?php echo __('all_rights_reserved'); ?></p>
                    <a href="admin/" class="text-light">
                        <i class="fas fa-cog me-1"></i><?php echo __('admin'); ?>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
    <script src="assets/js/gallery.js"></script>
</body>
</html>
