# معرض الصور المتعدد اللغات | Multilingual Photo Gallery

معرض صور احترافي يدعم اللغتين العربية والإنجليزية مع لوحة إدارة متكاملة.

A professional photo gallery that supports both Arabic and English languages with a complete admin panel.

## المميزات | Features

### 🌐 دعم متعدد اللغات | Multilingual Support
- دعم كامل للغتين العربية والإنجليزية
- واجهة RTL للعربية و LTR للإنجليزية
- ترجمة شاملة لجميع عناصر الواجهة

### 📸 إدارة الصور | Image Management
- رفع متعدد للصور مع دعم السحب والإفلات
- إنشاء تلقائي للصور المصغرة والمتوسطة
- استخراج وعرض بيانات EXIF
- تصنيف الصور بالأصناف والعلامات
- بحث متقدم في الصور

### 🎨 واجهة مستخدم حديثة | Modern UI
- تصميم متجاوب يعمل على جميع الأجهزة
- استخدام Bootstrap 5 للتصميم
- تأثيرات بصرية جذابة
- عارض صور Lightbox متقدم

### ⚙️ لوحة إدارة متكاملة | Complete Admin Panel
- لوحة تحكم شاملة مع الإحصائيات
- إدارة الصور والأصناف والعلامات
- نظام مصادقة آمن
- رفع متعدد للصور

## متطلبات النظام | System Requirements

- PHP 7.4 أو أحدث | PHP 7.4 or higher
- MySQL 5.7 أو أحدث | MySQL 5.7 or higher
- خادم ويب (Apache/Nginx) | Web Server (Apache/Nginx)
- امتدادات PHP المطلوبة | Required PHP Extensions:
  - PDO
  - GD أو ImageMagick | GD or ImageMagick
  - EXIF
  - JSON
  - mbstring

## التثبيت | Installation

### 1. تحميل الملفات | Download Files
```bash
git clone https://github.com/your-repo/photo-gallery.git
cd photo-gallery
```

### 2. إعداد قاعدة البيانات | Database Setup
1. أنشئ قاعدة بيانات جديدة | Create a new database
2. استورد ملف `database/schema.sql` | Import `database/schema.sql`
```sql
mysql -u username -p database_name < database/schema.sql
```

### 3. إعداد الملفات | File Configuration
1. انسخ `config/config.example.php` إلى `config/config.php`
2. عدّل إعدادات قاعدة البيانات في `config/config.php`

```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'photo_gallery');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 4. إعداد الصلاحيات | Set Permissions
```bash
chmod 755 uploads/
chmod 755 uploads/thumbnails/
chmod 755 uploads/medium/
chmod 644 config/config.php
```

### 5. الوصول للإدارة | Admin Access
- الرابط: `http://yoursite.com/admin/`
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

**⚠️ مهم: غيّر كلمة المرور الافتراضية فوراً!**

## الاستخدام | Usage

### الواجهة العامة | Public Interface
- الصفحة الرئيسية: `index.php`
- عرض صورة مفردة: `image.php?id=1`
- تصفية حسب الصنف: `index.php?category=1`
- تصفية حسب العلامة: `index.php?tag=1`
- البحث: `index.php?search=keyword`

### لوحة الإدارة | Admin Panel
- لوحة التحكم: `admin/index.php`
- رفع الصور: `admin/upload.php`
- إدارة الصور: `admin/images.php`
- إدارة الأصناف: `admin/categories.php`
- إدارة العلامات: `admin/tags.php`

## هيكل المشروع | Project Structure

```
photo-gallery/
├── admin/                  # لوحة الإدارة
│   ├── assets/            # ملفات CSS/JS للإدارة
│   ├── includes/          # ملفات مشتركة
│   ├── ajax/              # معالجات AJAX
│   └── *.php              # صفحات الإدارة
├── assets/                # الملفات الثابتة
│   ├── css/               # ملفات الأنماط
│   └── js/                # ملفات JavaScript
├── classes/               # فئات PHP
├── config/                # ملفات الإعداد
├── database/              # ملفات قاعدة البيانات
├── includes/              # ملفات مساعدة
├── languages/             # ملفات الترجمة
├── uploads/               # مجلد الصور المرفوعة
│   ├── thumbnails/        # الصور المصغرة
│   └── medium/            # الصور المتوسطة
├── index.php              # الصفحة الرئيسية
├── image.php              # صفحة عرض الصورة
└── init.php               # ملف التهيئة الرئيسي
```

## الإعدادات | Configuration

### إعدادات أساسية | Basic Settings
يمكن تعديل الإعدادات في `config/config.php`:

```php
// إعدادات الموقع
define('SITE_NAME', 'معرض الصور');
define('SITE_DESCRIPTION', 'معرض صور احترافي');
define('DEFAULT_LANGUAGE', 'ar');

// إعدادات الصور
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('THUMBNAIL_WIDTH', 300);
define('THUMBNAIL_HEIGHT', 300);
define('MEDIUM_WIDTH', 800);
define('MEDIUM_HEIGHT', 600);

// إعدادات العرض
define('IMAGES_PER_PAGE', 12);
define('ADMIN_IMAGES_PER_PAGE', 20);
```

### إعدادات متقدمة | Advanced Settings
- تفعيل/إلغاء وضع التطوير: `DEBUG_MODE`
- إعدادات الأمان: `SECURITY_SALT`
- إعدادات الجلسة: `SESSION_TIMEOUT`

## الأمان | Security

### تدابير الأمان المطبقة | Implemented Security Measures
- تشفير كلمات المرور باستخدام bcrypt
- حماية من هجمات SQL Injection
- حماية من هجمات XSS
- تحديد محاولات تسجيل الدخول
- تنظيف وتحقق من المدخلات
- حماية الملفات الحساسة

### توصيات إضافية | Additional Recommendations
- استخدم HTTPS في الإنتاج
- قم بتحديث كلمات المرور بانتظام
- راقب ملفات السجل
- قم بعمل نسخ احتياطية منتظمة

## الصيانة | Maintenance

### النسخ الاحتياطي | Backup
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p database_name > backup.sql

# نسخ احتياطي للصور
tar -czf uploads_backup.tar.gz uploads/
```

### التنظيف | Cleanup
- تشغيل `admin/cleanup.php` لتنظيف البيانات القديمة
- حذف الصور غير المستخدمة
- تحديث عدادات العلامات

## استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues

**لا يمكن رفع الصور:**
- تحقق من صلاحيات مجلد `uploads/`
- تحقق من إعدادات PHP: `upload_max_filesize`, `post_max_size`
- تحقق من امتدادات PHP المطلوبة

**مشاكل في العرض:**
- تحقق من مسارات الملفات في `config/config.php`
- تأكد من وجود ملفات CSS و JavaScript
- تحقق من إعدادات الخادم

**مشاكل في قاعدة البيانات:**
- تحقق من إعدادات الاتصال
- تأكد من استيراد ملف `schema.sql` بشكل صحيح
- تحقق من صلاحيات المستخدم في قاعدة البيانات

## المساهمة | Contributing

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات مع الاختبارات
4. إرسال Pull Request

## الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم | Support

للحصول على الدعم:
- افتح Issue في GitHub
- راسلنا على: <EMAIL>
- راجع الوثائق في مجلد `docs/`

## الشكر والتقدير | Acknowledgments

- Bootstrap لإطار العمل
- Font Awesome للأيقونات
- Lightbox للعارض
- جميع المساهمين في المشروع

---

**تم تطوير هذا المشروع بحب ❤️ للمجتمع العربي والعالمي**

**This project was developed with love ❤️ for the Arabic and global community**
