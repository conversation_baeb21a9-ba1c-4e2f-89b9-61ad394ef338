<?php
/**
 * فئة رفع الملفات
 * File Upload Class
 */

class FileUploader {
    private $uploadDir;
    private $allowedTypes;
    private $maxFileSize;
    private $errors = [];
    
    public function __construct() {
        $this->uploadDir = ROOT_PATH . '/' . UPLOAD_DIR;
        $this->allowedTypes = ALLOWED_MIME_TYPES;
        $this->maxFileSize = MAX_FILE_SIZE;
        
        // التأكد من وجود مجلد الرفع
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }
    
    /**
     * رفع صورة واحدة
     */
    public function uploadImage($file, $options = []) {
        $this->errors = [];
        
        // التحقق من وجود الملف
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            $this->errors[] = 'لم يتم اختيار ملف';
            return false;
        }
        
        // التحقق من أخطاء الرفع
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $this->errors[] = $this->getUploadErrorMessage($file['error']);
            return false;
        }
        
        // التحقق من حجم الملف
        if ($file['size'] > $this->maxFileSize) {
            $this->errors[] = 'حجم الملف كبير جداً. الحد الأقصى: ' . formatFileSize($this->maxFileSize);
            return false;
        }
        
        // التحقق من نوع الملف
        $mimeType = mime_content_type($file['tmp_name']);
        if (!in_array($mimeType, $this->allowedTypes)) {
            $this->errors[] = 'نوع الملف غير مدعوم';
            return false;
        }
        
        // التحقق من أن الملف صورة حقيقية
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            $this->errors[] = 'الملف ليس صورة صحيحة';
            return false;
        }
        
        // إنشاء اسم ملف فريد
        $originalName = $file['name'];
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        $filename = $this->generateUniqueFilename($extension);
        $filePath = $this->uploadDir . $filename;
        
        // نقل الملف
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            $this->errors[] = 'فشل في رفع الملف';
            return false;
        }
        
        // إنشاء المصغرات
        $imageProcessor = new ImageProcessor();
        $thumbnailPath = $imageProcessor->createThumbnail($filePath, $filename);
        $mediumPath = $imageProcessor->createMediumImage($filePath, $filename);
        
        // استخراج بيانات EXIF
        $exifData = $imageProcessor->extractExifData($filePath);
        
        return [
            'filename' => $filename,
            'original_filename' => $originalName,
            'file_path' => UPLOAD_DIR . $filename,
            'thumbnail_path' => $thumbnailPath,
            'medium_path' => $mediumPath,
            'file_size' => $file['size'],
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'mime_type' => $mimeType,
            'exif_data' => $exifData
        ];
    }
    
    /**
     * رفع عدة صور
     */
    public function uploadMultipleImages($files) {
        $results = [];
        $errors = [];
        
        // تحويل تنسيق الملفات المتعددة
        $fileArray = $this->reArrayFiles($files);
        
        foreach ($fileArray as $file) {
            $result = $this->uploadImage($file);
            if ($result) {
                $results[] = $result;
            } else {
                $errors = array_merge($errors, $this->errors);
            }
        }
        
        $this->errors = $errors;
        return $results;
    }
    
    /**
     * إنشاء اسم ملف فريد
     */
    private function generateUniqueFilename($extension) {
        do {
            $filename = uniqid() . '_' . time() . '.' . $extension;
            $filePath = $this->uploadDir . $filename;
        } while (file_exists($filePath));
        
        return $filename;
    }
    
    /**
     * تحويل تنسيق الملفات المتعددة
     */
    private function reArrayFiles($filePost) {
        $fileArray = [];
        $fileCount = count($filePost['name']);
        $fileKeys = array_keys($filePost);
        
        for ($i = 0; $i < $fileCount; $i++) {
            foreach ($fileKeys as $key) {
                $fileArray[$i][$key] = $filePost[$key][$i];
            }
        }
        
        return $fileArray;
    }
    
    /**
     * الحصول على رسالة خطأ الرفع
     */
    private function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'حجم الملف يتجاوز الحد المسموح في إعدادات الخادم';
            case UPLOAD_ERR_FORM_SIZE:
                return 'حجم الملف يتجاوز الحد المسموح في النموذج';
            case UPLOAD_ERR_PARTIAL:
                return 'تم رفع الملف جزئياً فقط';
            case UPLOAD_ERR_NO_FILE:
                return 'لم يتم رفع أي ملف';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'مجلد الملفات المؤقتة غير موجود';
            case UPLOAD_ERR_CANT_WRITE:
                return 'فشل في كتابة الملف على القرص';
            case UPLOAD_ERR_EXTENSION:
                return 'امتداد PHP أوقف رفع الملف';
            default:
                return 'خطأ غير معروف في رفع الملف';
        }
    }
    
    /**
     * التحقق من صحة الصورة
     */
    public function validateImage($file) {
        $this->errors = [];
        
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            $this->errors[] = 'لم يتم اختيار ملف';
            return false;
        }
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $this->errors[] = $this->getUploadErrorMessage($file['error']);
            return false;
        }
        
        if ($file['size'] > $this->maxFileSize) {
            $this->errors[] = 'حجم الملف كبير جداً';
            return false;
        }
        
        $mimeType = mime_content_type($file['tmp_name']);
        if (!in_array($mimeType, $this->allowedTypes)) {
            $this->errors[] = 'نوع الملف غير مدعوم';
            return false;
        }
        
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            $this->errors[] = 'الملف ليس صورة صحيحة';
            return false;
        }
        
        return true;
    }
    
    /**
     * الحصول على الأخطاء
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * الحصول على آخر خطأ
     */
    public function getLastError() {
        return end($this->errors);
    }
    
    /**
     * حذف ملف
     */
    public function deleteFile($filename) {
        $filePath = $this->uploadDir . $filename;
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        return false;
    }
    
    /**
     * الحصول على معلومات الملف
     */
    public function getFileInfo($filename) {
        $filePath = $this->uploadDir . $filename;
        
        if (!file_exists($filePath)) {
            return false;
        }
        
        $imageInfo = getimagesize($filePath);
        
        return [
            'filename' => $filename,
            'file_path' => $filePath,
            'file_size' => filesize($filePath),
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'mime_type' => $imageInfo['mime'],
            'created' => filemtime($filePath)
        ];
    }
    
    /**
     * تنظيف الملفات القديمة
     */
    public function cleanupOldFiles($days = 30) {
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $deletedCount = 0;
        
        $files = glob($this->uploadDir . '*');
        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $cutoffTime) {
                // التحقق من أن الملف غير مرتبط بأي صورة في قاعدة البيانات
                $filename = basename($file);
                $db = Database::getInstance();
                $exists = $db->selectOne("SELECT id FROM images WHERE filename = ?", [$filename]);
                
                if (!$exists) {
                    unlink($file);
                    $deletedCount++;
                }
            }
        }
        
        return $deletedCount;
    }
    
    /**
     * الحصول على حجم مجلد الرفع
     */
    public function getUploadDirSize() {
        $size = 0;
        $files = glob($this->uploadDir . '*');
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $size += filesize($file);
            }
        }
        
        return $size;
    }
    
    /**
     * إنشاء نسخة احتياطية من الملفات
     */
    public function backupFiles($backupDir) {
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        $files = glob($this->uploadDir . '*');
        $copiedCount = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $filename = basename($file);
                $backupPath = $backupDir . '/' . $filename;
                
                if (copy($file, $backupPath)) {
                    $copiedCount++;
                }
            }
        }
        
        return $copiedCount;
    }
}
?>
