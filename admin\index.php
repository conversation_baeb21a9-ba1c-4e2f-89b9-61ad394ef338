<?php
/**
 * لوحة الإدارة الرئيسية
 * Admin Dashboard
 */

require_once '../init.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$currentUser = $auth->getCurrentUser();

// إنشاء كائنات الإدارة
$imageManager = new ImageManager();
$categoryManager = new CategoryManager();
$tagManager = new TagManager();

// الحصول على الإحصائيات
$stats = [
    'total_images' => $db->count('images', 'is_active = 1'),
    'total_categories' => $db->count('categories'),
    'total_tags' => $db->count('tags'),
    'total_views' => $db->selectOne("SELECT SUM(views_count) as total FROM images WHERE is_active = 1")['total'] ?? 0,
    'recent_uploads' => $db->count('images', 'upload_date >= DATE_SUB(NOW(), INTERVAL 7 DAY) AND is_active = 1'),
    'storage_used' => $db->selectOne("SELECT SUM(file_size) as total FROM images WHERE is_active = 1")['total'] ?? 0
];

// الحصول على الصور الأحدث
$recentImages = $imageManager->getRecentImages(8);

// الحصول على الأصناف الأكثر استخداماً
$popularCategories = $categoryManager->getPopularCategories(5);

// الحصول على العلامات الأكثر استخداماً
$popularTags = $tagManager->getPopularTags(10);

$pageTitle = __('admin_dashboard') . META_TITLE_SEPARATOR . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="<?php echo $currentLang; ?>" dir="<?php echo $isRTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
    <?php if ($isRTL): ?>
    <link href="../assets/css/rtl.css" rel="stylesheet">
    <link href="assets/css/admin-rtl.css" rel="stylesheet">
    <?php endif; ?>
</head>
<body class="admin-body">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-cog me-2"></i>
                <?php echo __('admin_panel'); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-tachometer-alt me-1"></i><?php echo __('dashboard'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="images.php">
                            <i class="fas fa-images me-1"></i><?php echo __('images'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="upload.php">
                            <i class="fas fa-upload me-1"></i><?php echo __('upload'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="categories.php">
                            <i class="fas fa-folder me-1"></i><?php echo __('categories'); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tags.php">
                            <i class="fas fa-tags me-1"></i><?php echo __('tags'); ?>
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i><?php echo __('view_site'); ?>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name'] ?: $currentUser['username']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user-edit me-2"></i><?php echo __('profile'); ?>
                            </a></li>
                            <li><a class="dropdown-item" href="settings.php">
                                <i class="fas fa-cog me-2"></i><?php echo __('settings'); ?>
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout'); ?>
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1"><?php echo __('dashboard'); ?></h1>
                <p class="text-muted mb-0"><?php echo __('welcome_back'); ?>, <?php echo htmlspecialchars($currentUser['full_name'] ?: $currentUser['username']); ?></p>
            </div>
            <div>
                <a href="upload.php" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i><?php echo __('upload_images'); ?>
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-3 mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="card stat-card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><?php echo __('total_images'); ?></h5>
                                <h2 class="mb-0"><?php echo number_format($stats['total_images']); ?></h2>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-images fa-2x"></i>
                            </div>
                        </div>
                        <small class="opacity-75">
                            <i class="fas fa-plus me-1"></i>
                            <?php echo $stats['recent_uploads']; ?> <?php echo __('this_week'); ?>
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="card stat-card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><?php echo __('total_categories'); ?></h5>
                                <h2 class="mb-0"><?php echo number_format($stats['total_categories']); ?></h2>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-folder fa-2x"></i>
                            </div>
                        </div>
                        <small class="opacity-75">
                            <i class="fas fa-chart-line me-1"></i>
                            <?php echo count($popularCategories); ?> <?php echo __('active'); ?>
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="card stat-card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><?php echo __('total_views'); ?></h5>
                                <h2 class="mb-0"><?php echo number_format($stats['total_views']); ?></h2>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-eye fa-2x"></i>
                            </div>
                        </div>
                        <small class="opacity-75">
                            <i class="fas fa-trending-up me-1"></i>
                            <?php echo __('all_time'); ?>
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="card stat-card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title"><?php echo __('storage_used'); ?></h5>
                                <h2 class="mb-0"><?php echo formatFileSize($stats['storage_used']); ?></h2>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-hdd fa-2x"></i>
                            </div>
                        </div>
                        <small class="opacity-75">
                            <i class="fas fa-database me-1"></i>
                            <?php echo $stats['total_tags']; ?> <?php echo __('tags'); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Images -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock me-2"></i><?php echo __('recent_uploads'); ?>
                        </h5>
                        <a href="images.php" class="btn btn-sm btn-outline-primary">
                            <?php echo __('view_all'); ?>
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recentImages)): ?>
                        <div class="row g-3">
                            <?php foreach ($recentImages as $image): ?>
                            <div class="col-xl-3 col-lg-4 col-md-6">
                                <div class="card recent-image-card">
                                    <div class="position-relative">
                                        <img src="<?php echo '../' . THUMBNAILS_DIR . basename($image['thumbnail_path']); ?>" 
                                             class="card-img-top" 
                                             alt="<?php echo htmlspecialchars($currentLang === 'ar' ? $image['alt_text_ar'] : $image['alt_text']); ?>"
                                             style="height: 150px; object-fit: cover;">
                                        <div class="image-overlay">
                                            <div class="image-actions">
                                                <a href="../image.php?id=<?php echo $image['id']; ?>" 
                                                   class="btn btn-light btn-sm" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit-image.php?id=<?php echo $image['id']; ?>" 
                                                   class="btn btn-light btn-sm">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body p-2">
                                        <h6 class="card-title mb-1 text-truncate">
                                            <?php echo htmlspecialchars($currentLang === 'ar' ? $image['title_ar'] : $image['title']); ?>
                                        </h6>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i><?php echo $image['views_count']; ?>
                                            </small>
                                            <small class="text-muted">
                                                <?php echo formatDate($image['upload_date'], 'M j'); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5><?php echo __('no_images_yet'); ?></h5>
                            <p class="text-muted"><?php echo __('start_by_uploading'); ?></p>
                            <a href="upload.php" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i><?php echo __('upload_now'); ?>
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Popular Categories -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar me-2"></i><?php echo __('popular_categories'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($popularCategories)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($popularCategories as $category): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <div>
                                    <strong><?php echo htmlspecialchars($currentLang === 'ar' ? $category['name_ar'] : $category['name']); ?></strong>
                                </div>
                                <span class="badge bg-primary rounded-pill"><?php echo $category['image_count']; ?></span>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="text-center mt-3">
                            <a href="categories.php" class="btn btn-sm btn-outline-primary">
                                <?php echo __('manage_categories'); ?>
                            </a>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-folder fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-2"><?php echo __('no_categories_yet'); ?></p>
                            <a href="categories.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus me-1"></i><?php echo __('add_category'); ?>
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Popular Tags -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tags me-2"></i><?php echo __('popular_tags'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($popularTags)): ?>
                        <div class="tag-cloud">
                            <?php foreach ($popularTags as $tag): ?>
                            <span class="badge bg-light text-dark me-1 mb-1">
                                <?php echo htmlspecialchars($currentLang === 'ar' ? $tag['name_ar'] : $tag['name']); ?>
                                <span class="small">(<?php echo $tag['usage_count']; ?>)</span>
                            </span>
                            <?php endforeach; ?>
                        </div>
                        <div class="text-center mt-3">
                            <a href="tags.php" class="btn btn-sm btn-outline-primary">
                                <?php echo __('manage_tags'); ?>
                            </a>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-tags fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-2"><?php echo __('no_tags_yet'); ?></p>
                            <a href="tags.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus me-1"></i><?php echo __('add_tag'); ?>
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/gallery.js"></script>
    <script src="assets/js/admin.js"></script>
</body>
</html>
